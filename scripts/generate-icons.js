// This script uses sharp to generate icons for the PWA
import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import {fileURLToPath} from 'url';

console.log('Generating app icons...');

// Get the directory name in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const SOURCE_ICON = path.join(__dirname, '../assets/app-icon.png');
const ICONS_DIR = path.join(__dirname, '../assets/icons');

// Ensure the icons directory exists
if(!fs.existsSync(ICONS_DIR)) {
  fs.mkdirSync(ICONS_DIR, {recursive: true});
}

// Generate standard PWA icons
const generateIcon = async (size, filename) => {
  await sharp(SOURCE_ICON)
    .resize(size, size)
    .toFile(path.join(ICONS_DIR, filename));
  console.log(`Generated ${filename}`);
};

// Generate all required icons
const generateIcons = async () => {
  // Standard PWA icons
  await generateIcon(192, 'icon-192x192.png');
  await generateIcon(512, 'icon-512x512.png');

  // Android specific
  await generateIcon(32, 'icon-32x32.png'); // ldpi
  await generateIcon(48, 'icon-48x48.png'); // mdpi
  await generateIcon(72, 'icon-72x72.png'); // hdpi
  await generateIcon(96, 'icon-96x96.png'); // xhdpi
  // await generateIcon(192, 'icon-192x192.png'); // xxhdpi

  // iOS specific
  await generateIcon(180, 'apple-touch-icon.png');

  // Notification badge
  await generateIcon(96, 'badge-96x96.png');

  // Shortcut icon
  await generateIcon(96, 'walk-96x96.png');

  // Additional sizes for better device coverage
  await generateIcon(128, 'icon-128x128.png');
  await generateIcon(144, 'icon-144x144.png');
  await generateIcon(152, 'icon-152x152.png');
  await generateIcon(167, 'icon-167x167.png');
  await generateIcon(180, 'icon-180x180.png');
  await generateIcon(256, 'icon-256x256.png');
  await generateIcon(384, 'icon-384x384.png');

  // iOS splash screens (different sizes for different devices)
  // iPhone X (1125px × 2436px)
  await sharp(SOURCE_ICON)
    .resize(1125, 2436)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1} // #f6f2ef background
    })
    .toFile(path.join(ICONS_DIR, 'splash-1125x2436.png'));

  // iPhone 12 Pro (1170px × 2532px)
  await sharp(SOURCE_ICON)
    .resize(1170, 2532)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1170x2532.png'));

  // iPhone 12, 11 Pro (1125px × 2436px)
  await sharp(SOURCE_ICON)
    .resize(1125, 2436)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1125x2436.png'));

  // iPhone 11, XR (828px × 1792px)
  await sharp(SOURCE_ICON)
    .resize(828, 1792)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-828x1792.png'));

  // iPhone XS, X (1125px × 2436px)
  await sharp(SOURCE_ICON)
    .resize(1125, 2436)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1125x2436.png'));

  // iPhone 8 Plus, 7 Plus, 6s Plus, 6 Plus (1242px × 2208px)
  await sharp(SOURCE_ICON)
    .resize(1242, 2208)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1242x2208.png'));

  // iPhone SE (750px × 1334px)
  await sharp(SOURCE_ICON)
    .resize(750, 1334)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-750x1334.png'));

  // iPhone 14 Pro Max (1290px × 2796px)
  await sharp(SOURCE_ICON)
    .resize(1290, 2796)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1290x2796.png'));

  // iPhone 14 Pro (1170px × 2532px)
  await sharp(SOURCE_ICON)
    .resize(1170, 2532)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1170x2532.png'));

  // iPhone 14 Plus (1179px × 2556px)
  await sharp(SOURCE_ICON)
    .resize(1179, 2556)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1179x2556.png'));

  // iPhone 14 (1170px × 2532px)
  await sharp(SOURCE_ICON)
    .resize(1170, 2532)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1170x2532.png'));

  // iPhone 13 Pro Max (1284px × 2778px)
  await sharp(SOURCE_ICON)
    .resize(1284, 2778)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1284x2778.png'));

  // iPhone 13 Pro (1170px × 2532px)
  await sharp(SOURCE_ICON)
    .resize(1170, 2532)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1170x2532.png'));

  // iPhone 13 Pro (1170px × 2532px)
  await sharp(SOURCE_ICON)
    .resize(1170, 2532)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1170x2532.png'));

  // iPhone 13 Mini (1080px × 2340px)
  await sharp(SOURCE_ICON)
    .resize(1080, 2340)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1080x2340.png'));

  // iPhone 12 Pro Max (1284px × 2778px)
  await sharp(SOURCE_ICON)
    .resize(1284, 2778)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1284x2778.png'));

  // iPhone 12 Pro (1170px × 2532px)
  await sharp(SOURCE_ICON)
    .resize(1170, 2532)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1170x2532.png'));

  // iPhone 12 (1125px × 2436px)
  await sharp(SOURCE_ICON)
    .resize(1125, 2436)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1125x2436.png'));

  // iPhone 11 Pro Max (1242px × 2688px)
  await sharp(SOURCE_ICON)
    .resize(1242, 2688)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1242x2688.png'));

  // iPhone 11 Pro (1125px × 2436px)
  await sharp(SOURCE_ICON)
    .resize(1125, 2436)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-1125x2436.png'));

  // iPhone 11 (828px × 1792px)
  await sharp(SOURCE_ICON)
    .resize(828, 1792)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-828x1792.png'));

  // iPhone SE (2nd generation) (650px × 1194px)
  await sharp(SOURCE_ICON)
    .resize(650, 1194)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-650x1194.png'));

  // iPhone 8, 7, 6s, 6 (750px × 1334px)
  await sharp(SOURCE_ICON)
    .resize(750, 1334)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-750x1334.png'));

  // iPad Pro 12.9" (2048px × 2732px)
  await sharp(SOURCE_ICON)
    .resize(2048, 2732)
    .extend({
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      background: {r: 246, g: 242, b: 239, alpha: 1}
    })
    .toFile(path.join(ICONS_DIR, 'splash-2048x2732.png'));

  console.log('All icons generated successfully!');
};

// Run the icon generation
generateIcons().catch(err => {
  console.error('Error generating icons:', err);
  process.exit(1);
});

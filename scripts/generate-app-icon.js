import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import {fileURLToPath} from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const text = `my DOG\\nin   FIT`;
const width = 512;
const height = 512;

const svg = `
<svg width="${width}" height="${height}">
  <style>
    .title { fill: black; font-size: 60px; font-weight: bold; text-anchor: middle; font-family: sans-serif }
  </style>
  <text x="${width / 2}" y="${height / 2 - 30}" class="title">${text.split('\\n')[0]}</text>
  <text x="${width / 2}" y="${height / 2 + 40}" class="title">${text.split('\\n')[1]}</text>
</svg>
`;

const buffer = Buffer.from(svg);

sharp(buffer, {
  svg: {
    dpi: 300
  }
})
  .png()
  .toFile(path.join(__dirname, '../src/assets/app-icon.png'))
  .then(() => console.log('App icon generated successfully!'))
  .catch(err => console.error('Error generating app icon:', err));

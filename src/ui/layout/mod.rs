use dioxus::prelude::*;

pub mod bottom_navbar;
pub use bottom_navbar::*;

pub mod card;
pub use card::*;

pub mod col;
pub use col::*;

pub mod dialog;
pub use dialog::*;

pub mod page;
pub use page::*;

pub mod page_header;
pub use page_header::*;

pub mod row;
pub use row::*;

pub mod section;
pub use section::*;


#[derive(Props, PartialEq, Clone)]
pub struct ContainerProps {
    pub children: Element,
    #[props(default)]
    pub class:    String,
}

#[component]
pub fn Header(props: ContainerProps) -> Element {
    rsx! {
      div { class: "header {props.class}", {props.children} }
    }
}

#[component]
pub fn Title(props: ContainerProps) -> Element {
    rsx! {
      h3 { class: "title {props.class}", {props.children} }
    }
}

#[component]
pub fn Description(props: ContainerProps) -> Element {
    rsx! {
      p { class: "description {props.class}", {props.children} }
    }
}

#[component]
pub fn Content(props: ContainerProps) -> Element {
    rsx! {
      div { class: "content {props.class}", {props.children} }
    }
}

#[component]
pub fn Footer(props: ContainerProps) -> Element {
    rsx! {
      div { class: "footer {props.class}", {props.children} }
    }
}

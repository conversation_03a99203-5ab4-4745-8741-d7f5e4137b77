use dioxus::prelude::*;

#[derive(<PERSON>ial<PERSON>q, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct SectionProps {
    #[props(default = false)]
    pub colored:  bool,
    #[props(default)]
    pub class:    String,
    #[props(default)]
    pub children: Element,
}

#[component]
pub fn Section(props: SectionProps) -> Element {
    rsx! {
      div { class: if props.colored { "section-colored {props.class}" } else { "section {props.class}" },
        {props.children}
      }
    }
}

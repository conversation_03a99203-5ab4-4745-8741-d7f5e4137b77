use std::ops::Deref;

use dioxus::{
    logger::tracing::{
        debug,
        info,
    },
    prelude::*,
};
use dioxus_free_icons::{
    IconShape,
    icons::{
        fa_solid_icons::FaDog,
        ld_icons::{
            LdDog,
            LdHome,
            LdPlay,
            LdSettings,
            LdSquare,
        },
    },
};

// use dioxus_motion::prelude::*;
use crate::{
    models::{AppState, Theme, use_state, AppEvent, RadioReducer, AppAction},
    ui::{
        components::base::{
            Button,
            ButtonVariant,
            Icon,
        },
        layout::Col,
        routes::Route,
    },
    utils::misc::concat_classes,
};


struct NavbarItem {
    icon:        Option<&'static dyn IconShape>,
    label:       &'static str,
    path:        &'static str,
    route:       fn() -> Route,
    color_class: Option<&'static str>,
}

const NAV_ITEMS: [NavbarItem; 5] = [
    NavbarItem {
        icon:        Some(&LdHome),
        label:       "Home",
        path:        "/",
        route:       || Route::Home {},
        color_class: Some("icon-activity"),
    },
    NavbarItem {
        icon:        Some(&LdDog),
        label:       "Profile",
        path:        "/dog",
        route:       || Route::Home {},
        // route:       Route::DogProfile {
        //     id: "1".to_string()
        // },
        color_class: Some("icon-dog"),
    },
    NavbarItem {
        icon:        None,
        label:       "",
        path:        "",
        route:       || Route::Home {},
        color_class: None,
    },
    NavbarItem {
        icon:        Some(&FaDog),
        label:       "AI",
        path:        "/ai",
        route:       || Route::Settings {},
        color_class: Some("icon-food"),
    },
    NavbarItem {
        icon:        Some(&LdSettings),
        label:       "Settings",
        path:        "/settings",
        route:       || Route::Settings {},
        color_class: Some("icon-weight"),
    },
    // NavbarItem {
    //     icon:        Some(LD_SETTINGS),
    //     label:       "Onboarding",
    //     path:        "/onboarding",
    //     color_class: Some("icon-weight"),
    // },
];


#[component]
pub fn BottomNavbar() -> Element {
    // let is_walking = use_context::<AppState>().is_walking;
    let theme = use_context::<AppState>().theme;
    let current_path = use_route::<Route>().to_string();

    let get_icon_color = |is_active: bool| {
        if theme == Theme::Colored {
            if is_active { "text-white" } else { "text-gray-500" }
        } else if is_active {
            // "text-black"
            "text-secondary-foreground"
        } else {
            // "text-gray-400"
            "text-gray-800"
        }
    };

    let is_active = |item: &NavbarItem| current_path == item.path;

    rsx! {
        // if is_walking() {
        //   WalkStatusBar {}
        // }

        nav {
            class: "fixed bottom-2 left-5 right-5 px-1 py-1 flex justify-around items-end z-10 transition-colors duration-300 backdrop-blur-sm rounded-4xl shadow-md",
            // class: if theme == Theme::Light { "bg-background/35" } else if theme == Theme::Dark { "bg-card" } else { "bg-white/5" },
            class: if theme == Theme::Light { "bg-white/55" } else if theme == Theme::Dark { "bg-card" } else { "bg-white/5" },
            for (i , item) in NAV_ITEMS.iter().enumerate() {
                // let is_active = current_path == item.path;
                if item.icon.is_some() {
                    Link {
                        key: "{i}",
                        to: "{item.path}",
                        // to: (item.route)(),
                        class: concat_classes(
                            &[
                                "flex flex-col items-center justify-center px-4 pt-1 w-1/5",
                                get_icon_color(is_active(item)),
                                if is_active(item) { "bg-background-darker/40 rounded-4xl" } else { "" },
                                if theme == Theme::Colored { "hover:bg-white/10" } else { "" },
                            ],
                        ),
                        div { class: if is_active(item) { "animate-bounce-light" } else { "" },
                            // class: if theme == Theme::Colored && is_active(item) { item.color_class.as_deref().unwrap_or("") } else { "" },
                            Icon { size: 24, icon: item.icon.unwrap() }
                        }
                        span { class: "text-xs mt-0.5", "{item.label}" }
                    }
                } else {
                    div { class: "w-24 relative", key: "{i}",
                        // PulseEffect {}
                        WalkButton {}
                    }
                }
            }
        }
    }
}

#[component]
fn WalkButton() -> Element {
    info!("WalkButton()");
    // let navigator = use_navigator();
    let current_path = use_route::<Route>().to_string();
    let mut state = use_state(AppEvent::ActivityStateUpdated);
    let is_active = state.read().get_selected_dog().unwrap().is_active();
    let is_small = current_path == "/ai" || current_path == "/settings";

    rsx! {
        Col {
            class: concat_classes(
                &["transition-all duration-200", if is_small { "-mt-12" } else { "-mt-19" }],
            ),
            Button {
                class: concat_classes(
                    &[
                        "rounded-full col shadow-lg transition-all",
                        if is_small { "w-10 h-10" } else { "w-14 h-14" },
                    ],
                ),
                color: if is_active { "bg-pet-pink hover:bg-pet-pink/90" },
                onclick: move |_| {
                    state.apply(AppAction::ActivityToggle);
                },
                Icon {
                    class: "text-white",
                    size: 6,
                    icon: if is_active { &LdSquare } else { &LdPlay },
                }
            }
            div { class: "text-xs my-0.5 font-bold",
                if is_active {
                    ActivityTimer {}
                }
            }
        }
    }
}

#[component]
fn ActivityTimer() -> Element {
    let state = use_state(AppEvent::ActivityStateUpdated);

    // * Signal to force re-render every second
    let mut tick = use_signal(|| 0u64);

    // * Start a timer that updates every second when component mounts
    #[cfg(not(feature = "web"))]
    use_effect(move || {
        spawn(async move {
            loop {
                tokio::time::sleep(std::time::Duration::from_secs(1)).await;
                tick += 1;
            }
        });
    });

    let duration = use_memo(move || {
        // * Include tick in dependency to force recalculation every second
        let _ = tick();
        return state
            .read()
            .get_selected_dog()
            .unwrap()
            .get_current_activity_duration()
    });

    let format_duration = move || {
        let total_seconds = duration().num_seconds();
        let days = total_seconds / 60 / 60 / 24;
        let hours = total_seconds / 60 / 60 % 24;
        let minutes = total_seconds / 60 % 60;
        let seconds = total_seconds % 60;
        if days > 0 {
            format!("{days}d {hours}:{minutes:02}:{seconds:02}")
        } else {
            format!("{hours}:{minutes:02}:{seconds:02}")
        }
    };

    rsx! {
        div { class: "text-xs font-bold", "{format_duration()}" }
    }
}

// use dioxus::prelude::*;
// use dioxus_router::prelude::*;

// use crate::{
//     i18n::*,
//     models::*,
//     ui::components::{
//         onboarding::OnboardingSection,
//         page_header::PageHeader,
//     },
// };

// #[component]
// pub fn AddDogPage() -> Element {
//     let i18n = I18n::new();
//     let state = use_context::<AppState>();
//     let navigator = use_navigator();

//     let handle_complete = move |pet_data: Dog| {
//         if let Some(mut state) = state.write() {
//             state.pets.push(pet_data.clone());
//         }
//         // In Dioxus, you'd navigate using the navigator
//         navigator.push(format!("/results?petId={}", pet_data.id));
//         // log::info!("{}'s profile has been successfully created.", pet_data.name);
//     };

//     rsx!(
//       div { class: "pb-20",
//         PageHeader {
//           title: t!("addDog.title").to_string(),
//           back_path: "/".to_string(),
//         }

//         OnboardingSection { on_complete: handle_complete }
//       }
//     )
// }

use dioxus::prelude::*;

use crate::{
    i18n::t,
    models::Food,
};

#[derive(<PERSON>ps, PartialEq, Clone)]
pub struct FoodPortion {
    pub id:               String,
    pub name:             String,
    pub brand:            String,
    pub calories_per_cup: f64,
    pub is_preferred:     bool,
    pub icon:             String,
    pub cups_per_day:     f64,
}

#[component]
pub fn DailyDietCard(daily_calories: u32, preferred_foods: Vec<Food>, diet_adjustment: i32) -> Element {
    let active_foods: Vec<Food> = preferred_foods
        .iter()
        .filter(|food| food.is_preferred)
        .cloned()
        .collect();

    let calculate_food_portions = || -> Vec<FoodPortion> {
        if active_foods.is_empty() {
            return vec![];
        }

        let adjusted_calories = f64::from(daily_calories) * (1.0 + f64::from(diet_adjustment) / 100.0);
        let calories_per_food = adjusted_calories / active_foods.len() as f64;

        active_foods
            .iter()
            .map(|food| {
                let cups_per_day = calories_per_food / f64::from(food.calories_per_cup);
                FoodPortion {
                    id:               food.id.clone(),
                    name:             food.name.clone(),
                    brand:            food.brand.clone(),
                    calories_per_cup: f64::from(food.calories_per_cup),
                    is_preferred:     food.is_preferred,
                    icon:             food.icon.clone(),
                    cups_per_day:     (cups_per_day * 100.0).round() / 100.0, // Equivalent to toFixed(2)
                }
            })
            .collect()
    };

    let food_portions = calculate_food_portions();

    let get_food_icon = |icon_name: &str| -> Element {
        // In a real Dioxus app, you'd use an SVG component or an icon library.
        // For now, we'll use a simple text placeholder.
        rsx! {
            div { class: "h-5 w-5 text-white", "{icon_name}" }
        }
    };

    return rsx! {
        div { class: "border-none",
            div { class: "p-5",
                div { class: "flex justify-between items-center mb-4",
                    h2 { class: "text-lg font-bold flex items-center", {t!("diet-todays-diet")} }
                    // Placeholder for Utensils icon
                    div { class: "h-5 w-5 mr-2 text-primary", "🍴" }
                    if diet_adjustment != 0 {
                        div {
                            class: format!(
                                "px-2 py-1 text-xs rounded-full {}",
                                if diet_adjustment < 0 {
                                    "bg-green-100 text-green-800"
                                } else {
                                    "bg-amber-100 text-amber-800"
                                },
                            ),
                                                // if diet_adjustment > 0 { format!("+{}%", diet_adjustment) } else { format!("{}%", diet_adjustment) }
                        }
                    }
                }

                if active_foods.is_empty() {
                    div { class: "text-center py-1",
                        // Placeholder for Dog icon
                        div { class: "h-8 w-8 mx-auto mb-2 text-gray-400", "🐶" }
                        p { class: "text-sm font-medium", {t!("diet-no-preferred-foods")} }
                        p { class: "text-xs text-gray-500 mt-1", {t!("diet-add-foods-prompt")} }
                    }
                } else {
                    div { class: "space-y-3",
                        for food in food_portions.iter() {
                            div {
                                key: "{food.id}",
                                class: "flex items-center justify-between bg-white/50 p-3 rounded-lg",
                                div { class: "flex items-center gap-2",
                                    div { class: "p-2 rounded-full bg-pet-purple",
                                        {get_food_icon(&food.icon)}
                                    }
                                    div {
                                        p { class: "font-medium", "{food.name}" }
                                        p { class: "text-xs text-gray-500", "{food.brand}" }
                                    }
                                }
                                div { class: "text-right",
                                    p { class: "font-bold", "{food.cups_per_day} {t!(\"diet-cups\")}" }
                                    p { class: "text-xs text-gray-500",
                                        "{((food.cups_per_day * food.calories_per_cup) as u32)} {t!(\"diet-kcal\")}"
                                    }
                                }
                            }
                        }

                        div { class: "pt-2 text-sm text-gray-700",
                            p { class: "flex justify-between",
                                span { "{t!(\"diet-totalDailyCalories\")}:" }
                                span { class: "font-semibold",
                                    "{((daily_calories as f64 * (1.0 + diet_adjustment as f64 / 100.0)) as u32)} {t!(\"diet-kcal\")}"
                                    if diet_adjustment != 0 {
                                        span { class: "text-xs text-gray-500 ml-1",
                                            "({t!(\"diet-adjusted\")})"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    };
}

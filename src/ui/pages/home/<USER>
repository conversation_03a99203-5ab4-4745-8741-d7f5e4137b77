use dioxus::prelude::*;

use crate::models::Dog<PERSON><PERSON>ommendation;

#[derive(<PERSON><PERSON>, <PERSON>ialEq, <PERSON>lone)]
pub struct DogRecommendationCardProps {
    pub recommendation:  DogRecommendation,
    pub on_mark_as_seen: EventHandler<String>,
    #[props(optional)]
    pub class:           Option<String>,
}

#[component]
pub fn DogRecommendationCard(props: DogRecommendationCardProps) -> Element {
    let mut expanded = use_signal(|| false);
    let mut is_visible = use_signal(|| true);

    if !is_visible() {
        return rsx! {
            div { class: "hidden" }
        };
    }

    let get_category_color = |category: &str| -> String {
        match category {
            "diet" => "bg-pet-yellow".to_string(),
            "activity" => "bg-pet-green".to_string(),
            "health" => "bg-pet-purple".to_string(),
            _ => "bg-pet-lightPurple".to_string(),
        }
    };

    rsx! {
        div {
            // class: "shadow-lg border border-gray-200 overflow-hidden bg-primary/20 backdrop-blur-2xl {props.class.as_deref().unwrap_or(\"\")}"
            class: "shadow-lg border border-gray-200 overflow-hidden bg-primary/20 backdrop-blur-2xl",
            div {
                class: "absolute top-2 right-2 cursor-pointer",
                onclick: move |_| {
                    is_visible.set(false);
                },
                "✖️"
            }
            div { class: "px-4 pt-6 pb-2", // Simulating CardContent
                div { class: "flex items-start",
                    div { class: "flex flex-col",
                        if props.recommendation.text.len() > 100 {
                            p {
                                // class: "text-md font-light {if expanded() { \"\" } else { \"line-clamp-2\" }}",
                                class: "text-md font-light",
                                onclick: move |_| {
                                    expanded.set(!expanded());
                                },
                                "{props.recommendation.text}"
                                if expanded() {
                                    " ▲"
                                } else {
                                    " ▼"
                                }
                            }
                        } else {
                            p { class: "text-md font-medium", "{props.recommendation.text}" }
                        }

                        if !props.recommendation.seen {
                            button {
                                class: "mt-0 text-md self-end", // Simulating Button variant="ghost" size="sm"
                                onclick: move |_| {
                                    props.on_mark_as_seen.call(props.recommendation.id.clone());
                                },
                                "Next tip"
                            }
                        }
                    }
                }
            }
        }
    }
}

// use dioxus::prelude::*;
// use dioxus_router::prelude::*;

// use crate::{
//     i18n::*,
//     models::*,
//     ui::components::{
//         BreedSelector, // Placeholder, will need to be implemented
//         DeleteDogDialog,
//         EditDogProfileModal,
//         FoodManagement,
//         page_header::PageHeaderProfile,
//     },
// };

// #[component]
// pub fn DogProfilePage(id: String) -> Element {
//     let i18n = I18n::new();
//     let state = use_context::<AppState>();
//     let navigator = use_navigator();

//     let pet_id = id.clone();
//     let pet = use_memo(move || state.pets.iter().find(|p| p.id == *pet_id).cloned());

//     let show_delete_dialog = use_signal(|| false);
//     let is_editing_name = use_signal(|| false);
//     let is_editing_breed = use_signal(|| false);

//     let pet_name_state = use_signal(|| pet.as_ref().map(|p| p.name.clone()).unwrap_or_default());
//     let pet_breed_state = use_signal(|| pet.as_ref().map(|p| p.breed.clone()).unwrap_or_default());

//     let handle_edit_name = move |_| {
//         is_editing_name.set(true);
//     };

//     let handle_save_name = move |_| {
//         if let Some(mut state) = state.write() {
//             if let Some(p) = state.pets.iter_mut().find(|p| p.id == pet_id) {
//                 p.name = pet_name_state.get().clone();
//             }
//         }
//         is_editing_name.set(false);
//     };

//     let handle_edit_breed = move |_| {
//         is_editing_breed.set(true);
//     };

//     let handle_save_breed = move |_| {
//         if let Some(mut state) = state.write() {
//             if let Some(p) = state.pets.iter_mut().find(|p| p.id == pet_id) {
//                 p.breed = pet_breed_state.get().clone();
//             }
//         }
//         is_editing_breed.set(false);
//     };

//     let handle_delete = move |_| {
//         if let Some(mut state) = state.write() {
//             state.pets.retain(|p| p.id != pet_id);
//             if state.pets.is_empty() {
//                 navigator.push("/");
//             }
//         }
//         show_delete_dialog.set(false);
//     };

//     let handle_food_preferred = move |food_id: String, is_preferred: bool, _start_date: Option<String>| {
//         if let Some(mut state) = state.write() {
//             if let Some(p) = state.pets.iter_mut().find(|p| p.id == pet_id) {
//                 if let Some(food) = p.preferred_foods.iter_mut().find(|f| f.id == food_id) {
//                     food.is_preferred = is_preferred;
//                 }
//             }
//         }
//     };

//     let handle_add_pet_food = move |food: Food| {
//         if let Some(mut state) = state.write() {
//             if let Some(p) = state.pets.iter_mut().find(|p| p.id == pet_id) {
//                 p.preferred_foods.push(food);
//             }
//         }
//     };

//     let handle_remove_pet_food = move |food_id: String| {
//         if let Some(mut state) = state.write() {
//             if let Some(p) = state.pets.iter_mut().find(|p| p.id == pet_id) {
//                 p.preferred_foods.retain(|f| f.id != food_id);
//             }
//         }
//     };

//     rsx!(
//         div { class: "",
//             PageHeaderProfile {}

//             if let Some(current_pet) = pet.as_ref() {
//                 div { class: "border-none shadow-xs overflow-hidden bg-background mb-0 rounded-3xl",
//                     div {
//                         class: "flex flex-col items-center justify-center text-gray-300
// hover:text-gray-200 m-6 {if current_pet.image.is_some() { \"relative\" } else { \"border-2 border-dashed
// border-secondary rounded-lg p-6 bg-secondary/5 text-secondary/80 hover:text-secondary m-6 mb-0\" }}",
//                         if let Some(image_url) = &current_pet.image {
//                             img {
//                                 src: "{image_url}",
//                                 alt: "{current_pet.name}",
//                                 class: "absolute w-full h-full object-cover shadow-xl rounded-lg",
//                             }
//                         }
//                         div { class: "mt-2 flex text-sm",
//                             label {
//                                 r#for: "file-upload",
//                                 class: "relative cursor-pointer rounded-md font-medium",
//                                 // Placeholder for Camera icon
//                                 div { class: "mx-auto h-12 w-12", "📸" }
//                                 if current_pet.image.is_none() {
//                                     span { class: "text-gray-800", {t!("dog-p")}rofile-uploadPhoto")}
// }                                 }
//                                 input {
//                                     id: "file-upload",
//                                     name: "file-upload",
//                                     r#type: "file",
//                                     class: "sr-only",
//                                     accept: "image/*",
//                                     // onChange: handlePhotoUpload, // Implement file upload later
//                                 }
//                             }
//                         }
//                     }

//                     div { class: "pb-28 pt-4 px-6",
//                         div { class: "space-y-3 my-4",
//                             div { class: "flex justify-between items-center",
//                                 div { class: "flex items-center text-sm",
// "{t!(\"dogProfile-name\")}:" }                                 if *is_editing_name.get() {
//                                     div { class: "flex items-center",
//                                         input {
//                                             value: "{pet_name_state.get()}",
//                                             oninput: move |evt| pet_name_state.set(evt.value.clone()),
//                                             class: "border rounded px-2 py-1",
//                                         }
//                                         button {
//                                             class: "text-secondary rounded-full",
//                                             onclick: handle_save_name,
//                                             {t!("common-save")}
//                                         }
//                                     }
//                                 } else {
//                                     div { class: "flex items-center",
//                                         span { class: "text-sm font-medium capitalize mr-2",
// "{current_pet.name}" }                                         button {
//                                             class: "text-secondary rounded-full",
//                                             onclick: handle_edit_name,
//                                             // Placeholder for Edit icon
//                                             div { class: "h-4 w-4", "✏️" }
//                                         }
//                                     }
//                                 }
//                             }

//                             div { class: "flex justify-between items-center",
//                                 div { class: "flex items-center text-sm",
// "{t!(\"dogProfile-breed\")}:" }                                 if *is_editing_breed.get() {
//                                     div { class: "flex items-center",
//                                         // Placeholder for BreedSelector
//                                         input {
//                                             value: "{pet_breed_state.get()}",
//                                             oninput: move |evt| pet_breed_state.set(evt.value.clone()),
//                                             class: "border rounded px-2 py-1",
//                                         }
//                                         button {
//                                             class: "text-secondary rounded-full",
//                                             onclick: handle_save_breed,
//                                             {t!("common-save")}
//                                         }
//                                     }
//                                 } else {
//                                     div { class: "flex items-center",
//                                         span { class: "text-sm font-medium capitalize mr-2",
// "{current_pet.breed.as_deref().unwrap_or(\"Not set\")}" }                                         button
// {                                             class: "text-secondary rounded-full",
//                                             onclick: handle_edit_breed,
//                                             // Placeholder for Edit icon
//                                             div { class: "h-4 w-4", "✏️" }
//                                         }
//                                     }
//                                 }
//                             }

//                             div { class: "flex justify-between items-center",
//                                 div { class: "flex items-center text-sm",
// "{t!(\"dogProfile-size\")}:" }                                 div { class: "flex items-center",
//                                     span { class: "text-sm font-medium capitalize mr-2",
// "{current_pet.size.as_deref().unwrap_or(\"Not set\")}" }                                     //
// Placeholder for EditDogProfileModal                                     EditDogProfileModal {
//                                         pet: current_pet.clone(),
//                                         field: "size".to_string(),
//                                         on_save: move |new_size: String| {
//                                             if let Some(mut state) = state.write() {
//                                                 if let Some(p) = state.pets.iter_mut().find(|p| p.id ==
// pet_id) {                                                     p.size = Some(new_size);
//                                                 }
//                                             }
//                                         },
//                                         on_cancel: move || {},
//                                     }
//                                 }
//                             }

//                             div { class: "flex justify-between items-center",
//                                 div { class: "flex items-center text-sm",
// "{t!(\"dogProfile-birthday\")}:" }                                 div { class: "flex items-center",
//                                     span { class: "text-sm font-medium capitalize mr-2",
// "{current_pet.birthday.as_deref().unwrap_or(\"Not set\")}" }                                     //
// Placeholder for EditDogProfileModal                                     EditDogProfileModal {
//                                         pet: current_pet.clone(),
//                                         field: "birthday".to_string(),
//                                         on_save: move |new_birthday: String| {
//                                             if let Some(mut state) = state.write() {
//                                                 if let Some(p) = state.pets.iter_mut().find(|p| p.id ==
// pet_id) {                                                     p.birthday = Some(new_birthday);
//                                                 }
//                                             }
//                                         },
//                                         on_cancel: move || {},
//                                     }
//                                 }
//                             }
//                         }

//                         button {
//                             class: "w-full text-white bg-pet-red hover:bg-pet-red/90",
//                             onclick: move |_| show_delete_dialog.set(true),
//                             "{t!(\"common-delete\")} {t!(\"common-dogProfile\")}"
//                         }
//                     }
//                 }

//                 DeleteDogDialog {
//                     is_open: *show_delete_dialog.get(),
//                     on_close: move || show_delete_dialog.set(false),
//                     on_delete: handle_delete,
//                     dog_name: current_pet.name.clone(),
//                 }

//                 div { class: "px-5 py-4 border-t",
//                     FoodManagement {
//                         pet_foods: current_pet.preferred_foods.clone(),
//                         food_history: current_pet.food_history.clone(),
//                         on_food_added: handle_add_pet_food,
//                         on_food_removed: handle_remove_pet_food,
//                         on_food_preferred: handle_food_preferred,
//                     }
//                 }
//             } else {
//                 div { "Pet not found." }
//             }
//         }
//     )
// }

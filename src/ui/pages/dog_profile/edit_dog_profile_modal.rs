// use dioxus::prelude::*;

// use crate::{
//     i18n::*,
//     models::*,
//     ui::components::SizeSelector, // Assuming SizeSelector is already converted
// };

// #[derive(Props, PartialEq, Clone)]
// pub struct EditDogProfileModalProps {
//     pub pet:       Dog,
//     pub field:     String,               // "size" or "birthday"
//     pub on_save:   EventHandler<String>, // Simplified to String for now
//     pub on_cancel: EventHandler<()>,
// }

// #[component]
// pub fn EditDogProfileModal(props: EditDogProfileModalProps) -> Element {
//     let i18n = I18n::new();
//     let open = use_signal(|| false);
//     let current_value = use_signal(|| {
//         if props.field == "size" {
//             props.pet.size.clone().unwrap_or_default()
//         } else if props.field == "birthday" {
//             props.pet.birthday.clone().unwrap_or_default()
//         } else {
//             String::new()
//         }
//     });

//     let handle_save = move |_| {
//         props.on_save.call(current_value.get().clone());
//         open.set(false);
//     };

//     let handle_cancel = move |_| {
//         props.on_cancel.call(());
//         open.set(false);
//     };

//     rsx!(
//         // Simulate Dialog
//         button {
//             class: "text-secondary rounded-full",
//             onclick: move |_| open.set(true),
//             // Placeholder for Edit icon
//             div { class: "h-4 w-4", "✏️" }
//         }

//         if *open.get() {
//             div {
//                 class: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
//                 div {
//                     class: "bg-white p-6 rounded-lg shadow-lg max-w-sm w-full",
//                     div { class: "mb-4",
//                         h2 { class: "text-lg font-bold",
//                             {t!("edit-d")}ogProfileModal-edit")}
//                             " "
//                             "{if props.field == \"size\" { t!(\"editDogProfileModal-size\") } else {
// t!(\"editDogProfileModal-birthday\") }}"                         }
//                         p { class: "text-sm text-gray-500 mt-2",
//                             "{t!(\"editDogProfileModal-updateDescription\", \"field\": if props.field ==
// \"size\" { t!(\"editDogProfileModal-size\") } else { t!(\"editDogProfileModal-birthday\") })}"
//                         }
//                     }
//                     div { class: "grid gap-4 py-4",
//                         if props.field == "size" {
//                             SizeSelector {
//                                 selected_size: current_value.get().clone(),
//                                 on_size_selected: move |s: String| current_value.set(s),
//                             }
//                         } else if props.field == "birthday" {
//                             // Simulate Calendar
//                             input {
//                                 r#type: "date",
//                                 value: "{current_value.get()}",
//                                 oninput: move |evt| current_value.set(evt.value.clone()),
//                                 class: "w-full border rounded px-3 py-2",
//                             }
//                         }
//                     }
//                     div { class: "flex justify-end space-x-2",
//                         button {
//                             class: "px-4 py-2 rounded-md border border-gray-300 hover:bg-gray-100",
//                             onclick: handle_cancel,
//                             {t!("common-cancel")}
//                         }
//                         button {
//                             class: "px-4 py-2 rounded-md bg-blue-500 text-white hover:bg-blue-600",
//                             onclick: handle_save,
//                             {t!("common-save")}
//                         }
//                     }
//                 }
//             }
//         }
//     )
// }

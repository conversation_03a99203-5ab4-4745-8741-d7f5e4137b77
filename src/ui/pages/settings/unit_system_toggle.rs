// use dioxus::prelude::*;

// use crate::i18n::*;

// #[derive(<PERSON><PERSON>, PartialEq, Clone)]
// pub struct UnitSystemToggleProps {
//     pub unit_system:           String, // "metric" or "imperial"
//     pub on_unit_system_change: EventHandler<String>,
// }

// #[component]
// pub fn UnitSystemToggle(props: UnitSystemToggleProps) -> Element {
//     rsx! {
//         div { class: "flex space-x-2",
//             // Simulate Tabs
//             div { class: "w-full",
//                 div { class: "flex", // Simulate TabsList
//                     button {
//                         class: "px-4 py-2 rounded-md {if props.unit_system == \"metric\" { \"bg-blue-500
// text-white\" } else { \"bg-gray-200\" }}",                         onclick: move |_|
// props.on_unit_system_change.call("metric".to_string()),
// {t!("settings-metric")}                     }
//                     button {
//                         class: "px-4 py-2 rounded-md {if props.unit_system == \"imperial\" {
// \"bg-blue-500 text-white\" } else { \"bg-gray-200\" }}",                         onclick: move |_|
// props.on_unit_system_change.call("imperial".to_string()),
// {t!("settings-imperial")}                     }
//                 }
//             }
//         }
//     }
// }

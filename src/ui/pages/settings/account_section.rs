use dioxus::prelude::*;

use crate::{
    i18n::*,
    models::state::{use_state, AppEvent, RadioReducer, AppAction},
    ui::{
        components::base::{
            Button,
            ButtonVariant,
        },
        layout::{
            Card,
            Content,
            Description,
            Header,
            Section,
            Title,
        },
    },
};


#[component]
pub fn AccountSection() -> Element {
    let mut state = use_state(AppEvent::UserAuthUpdated);
    let user = &state.read().user;

    rsx! {
        Section {
            Header {
                Title { "Account" }
                Description { "Manage your account settings." }
            }
            if user.authenticated {
                Content {
                    p {
                        "Welcome,"
                        {user.email.as_deref().unwrap_or("User")}
                    }
                    Button {
                        variant: ButtonVariant::Destructive,
                        onclick: move |_| {
                            state.apply(AppAction::UserLogout);
                        },
                        "Logout"
                    }
                }
            } else {
                Content {
                    p { "You are not logged in." }
                    Button {
                        onclick: move |_| {
                            state.apply(AppAction::UserLogin);
                        },
                        "Login"
                    }
                }
            }
        }
    }
}


// #[component]
// pub fn AccountSection(props: AccountSectionProps) -> Element {
//     rsx! {
//         div { class: "m-0 mb-10 shadow-none", // Simulating Card
//             div { class: "p-0", // Simulating CardContent
//                 div { class: "flex items-center justify-between mb-4",
//                     h2 { class: "text-lg font-bold", {t!("settings-account")} }
//                     // Placeholder for User icon
//                     div { class: "h-5 w-5 text-primary", "👤" }
//                 }

//                 div { class: "space-y-4",
//                     if props.is_authenticated {
//                         div { class: "flex items-center",
//                             // Placeholder for UserCircle icon
//                             div { class: "h-6 w-6 mr-2 text-pet-purple", "⚪" }
//                             div {
//                                 if let Some(user_profile) = &props.user {
//                                     p { class: "font-medium",
//                                         {user_profile.email.as_deref().unwrap_or("N/A")}
//                                     }
//                                     EmailVerificationStatus { user: user_profile.clone() }
//                                 }
//                             }
//                         }

//                         hr { class: "my-2" } // Separator

//                         div { class: "flex justify-between items-center",
//                             p { class: "font-medium", {t!("settings-profile-information")} }
//                             button { class: "text-pet-purple",
//                                 // onclick: handle_edit_profile, // Implement later
//                                 {t!("common-edit")}
//                             }
//                         }

//                         hr { class: "my-2" } // Separator

//                         div { class: "flex justify-between items-center",
//                             p { class: "font-medium", {t!("settings-password")} }
//                             button { class: "text-pet-purple",
//                                 // onclick: handle_change_password, // Implement later
//                                 {t!("common-change")}
//                             }
//                         }

//                         hr { class: "my-2" } // Separator

//                         div { class: "flex justify-between items-center",
//                             p { class: "font-medium", {t!("settings-connected-devices")} }
//                             button { class: "text-pet-purple",
//                                 // onclick: handle_manage_devices, // Implement later
//                                 {t!("common-manage")}
//                             }
//                         }

//                         hr { class: "my-2" } // Separator

//                         button {
//                             class: "w-full px-4 py-2 rounded-md bg-red-500 text-white hover:bg-red-600",
//                             onclick: move |_| props.on_logout_click.call(()),
//                             // Placeholder for LogOut icon
//                             div { class: "h-4 w-4 mr-2", "➡️" }
//                             {t!("auth-log-out")}
//                         }
//                     } else {
//                         div { class: "space-y-5",
//                             div { class: "flex items-center",
//                                 // Placeholder for Shield icon
//                                 // div { class: "h-5 w-5 mr-2 text-pet-purple", "🛡️" }
//                                 div {
//                                     p { class: "text-md", {t!("settings-local-data-only")} }
//                                     p { class: "text-md text-gray-500",
//                                         {t!("settings-sign-in-to-sync")}
//                                     }
//                                 }
//                             }

//                             button {
//                                 class: "w-full px-4 py-2 rounded-md bg-blue-500 text-white
// hover:bg-blue-600 flex items-center justify-center gap-2",                                 onclick: move
// |_| props.on_login_click.call(()),                                 // Placeholder for LogIn icon
//                                 div { class: "h-5 w-5", "⬅️" }
//                                 {t!("auth-sign-up-or-sign-in")}
//                             }
//                         }
//                     }
//                 }
//             }
//         }
//     }
// }

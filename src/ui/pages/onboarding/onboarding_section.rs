use dioxus::prelude::*;

#[derive(<PERSON><PERSON>, PartialEq, Clone)]
pub struct OnboardingSectionProps {
    pub title:    String,
    pub children: Element,
}

#[component]
pub fn OnboardingSection(props: OnboardingSectionProps) -> Element {
    rsx! {
      div { class: "p-4 my-4 bg-white rounded-lg shadow",
        h2 { class: "text-xl font-semibold mb-2", "{props.title}" }
        div { {props.children} }
      }
    }
}

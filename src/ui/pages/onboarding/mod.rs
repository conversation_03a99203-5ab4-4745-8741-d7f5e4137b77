use dioxus::prelude::*;
use dioxus_router::hooks::use_navigator;

mod basic_info_form;
use basic_info_form::BasicInfoForm;
mod pet_info_form;
use pet_info_form::PetInfoForm;
mod onboarding_section;
use onboarding_section::OnboardingSection;

#[component]
pub fn Onboarding() -> Element {
    let mut current_step = use_signal(|| 1);
    let navigator = use_navigator();
    let name = use_signal(String::new);
    let email = use_signal(String::new);
    let size = use_signal(String::new);

    rsx! {
      div { class: "p-4",
        h1 { class: "text-2xl font-bold text-center mb-4", "Welcome to MyDogInFit!" }
        if *current_step.read() == 1 {
          OnboardingSection { title: "Step 1: Basic Information",
            BasicInfoForm {
              name,
              email,
              on_next: move |()| current_step.set(2),
            }
          }
        } else {
          OnboardingSection { title: "Step 2: Pet Details",
            PetInfoForm {
              pet_name: name,
              pet_size: size,
              on_back: move |()| current_step.set(1),
              on_finish: move |()| {
                  navigator.push("/");
              },
            }
          }
        }
      }
    }
}


// use crate::{
//     i18n::*,
//     models::*,
//     ui::components::onboarding::OnboardingSection,
// };

// #[component]
// pub fn OnboardingPage() -> Element {
//     let i18n = I18n::new();
//     let state = use_context::<AppState>();
//     let navigator = use_navigator();

//     // Simulate useEffect for navigation
//     use_effect(|&num_pets| {
//         if num_pets > 0 {
//             navigator.push("/");
//         }
//         async {}
//     });

//     // Simulate useEffect for toast
//     use_effect(|&num_pets| {
//         if num_pets == 0 {
//             // In Dioxus, you'd typically have a global toast system or pass a callback
//             // For now, we'll just print to console or use a simple alert.
//             // log::info!("Welcome to My Dog In Fit! Let's set up your dog's profile to get started.");
//         }
//         async {}
//     });

//     let handle_complete = move |pet_data: Dog| {
//         if let Some(mut state) = state.write() {
//             state.pets.push(pet_data.clone());
//         }
//         // In Dioxus, you'd navigate using the navigator
//         navigator.push(format!("/results?petId={}", pet_data.id));
//         // log::info!("{}'s profile has been successfully created.", pet_data.name);
//     };

//     rsx!(
//       div { class: "pb-20",
//         div { class: "pt-8 px-6 text-center mb-6",
//           h1 { class: "text-2xl font-bold mb-2", {t!("onboarding-welcome-t")}itle")} }
//           p { class: "text-gray-600", {t!("onboarding-welcome-d")}escription")} }
//         }

//         OnboardingSection { on_complete: handle_complete }
//       }
//     )
// }

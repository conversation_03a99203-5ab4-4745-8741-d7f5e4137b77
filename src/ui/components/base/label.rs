use dioxus::prelude::*;

#[derive(<PERSON><PERSON>, PartialEq, <PERSON><PERSON>)]
pub struct LabelProps {
    pub children: Element,
    #[props(into)]
    pub r#for:    Option<String>,
}

#[component]
pub fn Label(props: LabelProps) -> Element {
    rsx! {
      label {
        class: "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
        r#for: props.r#for,
        {props.children}
      }
    }
}

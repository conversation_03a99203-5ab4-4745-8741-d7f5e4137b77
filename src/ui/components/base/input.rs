use dioxus::prelude::*;

#[derive(<PERSON><PERSON>, PartialEq, Eq, <PERSON><PERSON>)]
pub struct InputProps {
    #[props(into)]
    pub id:          String,
    #[props(into)]
    pub value:       Signal<String>,
    #[props(default = "text".to_string(), into)]
    pub r#type:      String,
    #[props(into)]
    pub placeholder: Option<String>,
    #[props(default)]
    pub class:       String,
}

#[component]
pub fn Input(mut props: InputProps) -> Element {
    let class = format!(
        "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm \
         ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium \
         placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 \
         focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed \
         disabled:opacity-50 {}",
        props.class
    );

    rsx! {
      input {
        id: "{props.id}",
        class: "{class}",
        r#type: "{props.r#type}",
        placeholder: props.placeholder.clone(),
        value: "{props.value}",
        oninput: move |evt| props.value.set(evt.value()),
      }
    }
}

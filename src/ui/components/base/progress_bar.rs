use dioxus::prelude::*;

#[component]
pub fn ProgressBar(title: String, current_value: u32, target_value: u32, unit: String) -> Element {
    let progress_percentage = (current_value as f32 / target_value as f32 * 100.0).min(100.0);

    rsx!(
      div { class: "w-full",
        div { class: "flex justify-between items-center mb-2",
          span { class: "text-sm font-medium text-gray-600", "{title}" }
          span { class: "text-sm font-medium", "{current_value}/{target_value} {unit}" }
        }
        div { class: "h-2 bg-gray-200 rounded-full",
          div {
            class: "h-2 bg-pet-purple rounded-full",
            style: "width: {progress_percentage}%",
          }
        }
      }
    )
}

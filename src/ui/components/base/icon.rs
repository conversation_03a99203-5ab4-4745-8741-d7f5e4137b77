use dioxus::prelude::*;
use dioxus_free_icons::IconShape;


/// Icon component Props
#[derive(<PERSON><PERSON>, <PERSON><PERSON>)]
pub struct IconProps {
    /// The icon shape to use.
    pub icon:  &'static dyn IconShape,
    /// The width and height of the `<svg>` element. Defaults to 20. Pass None to omit.
    #[props(default = Some(20))]
    pub size:  Option<u32>,
    /// The color to use for filling the icon. Defaults to "currentColor".
    #[props(default = "currentColor".to_string())]
    pub fill:  String,
    /// An class for the `<svg>` element.
    #[props(default = String::new())]
    pub class: String,
    /// An accessible, short-text description for the icon.
    pub title: Option<String>,
    /// The style of the `<svg>` element.
    pub style: Option<String>,
}

impl PartialEq for IconProps {
    fn eq(&self, other: &Self) -> bool {
        // Compare by pointer address since we can't compare trait objects directly
        std::ptr::eq(self.icon, other.icon)
            && self.size == other.size
            && self.fill == other.fill
            && self.class == other.class
            && self.title == other.title
            && self.style == other.style
    }
}

/// Icon component which generates SVG elements
#[allow(non_snake_case)]
pub fn Icon(props: IconProps) -> Element {
    let (fill, stroke, stroke_width) = props.icon.fill_and_stroke(&props.fill);
    let size = props.size.unwrap().to_string();
    rsx!(
      svg {
        class: "{props.class}",
        style: props.style,
        height: size.clone(),
        width: size,
        view_box: "{props.icon.view_box()}",
        xmlns: "{props.icon.xmlns()}",
        fill,
        stroke,
        stroke_width,
        stroke_linecap: "{props.icon.stroke_linecap()}",
        stroke_linejoin: "{props.icon.stroke_linejoin()}",
        if let Some(title_text) = props.title {
          title { "{title_text}" }
        }
        {props.icon.child_elements()}
      }
    )
}

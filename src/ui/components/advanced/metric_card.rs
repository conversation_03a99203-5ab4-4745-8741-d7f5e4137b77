use dioxus::prelude::*;

#[component]
pub fn Metric<PERSON>ard(
    color: String,
    icon: String,
    title: String,
    unit: String,
    value: String,
    class: Option<String>,
) -> Element {
    return rsx! {
      div { class: "shadow-xs overflow-hidden transition-all bg-primary/15",
        div { class: "px-4 py-3 flex items-center space-x-2",
          div { class: "flex-1",
            p { class: "text-md text-black font-medium mb-2", "{title}" }
            div { class: "flex items-center justify-start space-x-2",
              div { class: "p-3 rounded-full flex items-center justify-center bg-white/20",
                "Icon"
              }
              div {
                p { class: "text-3xl font-semibold text-black/85",
                  "{value} {unit}"
                }
              }
            }
          }
        }
      }
    };
}

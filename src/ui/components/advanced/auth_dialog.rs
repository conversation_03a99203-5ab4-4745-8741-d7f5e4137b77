// use std::collections::HashMap;

// use dioxus::prelude::*;

// use crate::i18n::*;

// #[derive(<PERSON>ps, PartialEq, Clone)]
// pub struct AuthDialogProps {
//     pub open:           bool,
//     pub on_open_change: EventHandler<bool>,
//     #[props(optional)]
//     pub on_success:     Option<EventHandler<()>>,
// }

// #[component]
// pub fn AuthDialog(props: AuthDialogProps) -> Element {
//     let i18n = I18n::new();

//     let email = use_signal(|| String::new());
//     let password = use_signal(|| String::new());
//     let is_loading = use_signal(|| false);
//     let is_apple_supported = use_signal(|| false); // Always false for now in Rust/Dioxus

//     // Simulate useEffect for Apple device check
//     use_effect(move || {
//         // In a real web environment, you'd check navigator.userAgent
//         // For Dioxus desktop/web, this might always be false or require platform-specific checks.
//         // is_apple_supported.set(false);
//         async {}
//     });

//     let handle_email_login = move |_| {
//         if email.get().is_empty() || password.get().is_empty() {
//             // Simulate toast
//             // log::warn!("Missing Fields: Please enter both email and password.");
//             return;
//         }

//         is_loading.set(true);
//         cx.spawn(async move {
//             // Simulate API call
//             dioxus::tokio::time::sleep(std::time::Duration::from_secs(1)).await;
//             let success = true; // Mock success
//             is_loading.set(false);

//             if success {
//                 props.on_open_change.call(false);
//                 if let Some(on_success) = &props.on_success {
//                     on_success.call(());
//                 }
//             } else {
//                 // Simulate toast for failure
//                 // log::error!("Login failed.");
//             }
//         });
//     };

//     let handle_email_signup = move |_| {
//         if email.get().is_empty() || password.get().is_empty() {
//             // Simulate toast
//             // log::warn!("Missing Fields: Please enter both email and password.");
//             return;
//         }

//         if password.get().len() < 8 {
//             // Simulate toast
//             // log::warn!("Password Too Short: Password must be at least 8 characters long.");
//             return;
//         }

//         is_loading.set(true);
//         cx.spawn(async move {
//             // Simulate API call
//             dioxus::tokio::time::sleep(std::time::Duration::from_secs(1)).await;
//             let success = true; // Mock success
//             is_loading.set(false);

//             if success {
//                 // Simulate toast
//                 // log::info!("Check your email: We've sent you a verification link to complete
// sign-up.");             } else {
//                 // Simulate toast for failure
//                 // log::error!("Signup failed.");
//             }
//         });
//     };

//     let handle_google_auth = move |_| {
//         is_loading.set(true);
//         cx.spawn(async move {
//             // Simulate Google login
//             dioxus::tokio::time::sleep(std::time::Duration::from_secs(1)).await;
//             is_loading.set(false);
//             // In a real app, this would redirect or open a popup for OAuth
//         });
//     };

//     let handle_apple_auth = move |_| {
//         is_loading.set(true);
//         cx.spawn(async move {
//             // Simulate Apple login
//             dioxus::tokio::time::sleep(std::time::Duration::from_secs(1)).await;
//             is_loading.set(false);
//             // In a real app, this would redirect or open a popup for OAuth
//         });
//     };

//     let selected_tab = use_signal(|| "signup".to_string());

//     let render_social_buttons = rsx! {
//         div { class: "grid grid-cols-1 gap-2",
//             if *is_apple_supported.get() {
//                 button {
//                     class: "w-full px-4 py-2 border rounded-md hover:bg-gray-100",
//                     onclick: handle_apple_auth,
//                     disabled: *is_loading.get(),
//                     "🍎 {t!(\"auth-continueWithApple\")}" // Placeholder for Apple icon
//                 }
//             }

//             button {
//                 class: "w-full px-4 py-2 border rounded-md hover:bg-gray-100",
//                 onclick: handle_google_auth,
//                 disabled: *is_loading.get(),
//                 "G {t!(\"auth-continueWithGoogle\")}" // Placeholder for Google icon
//             }

//             div { class: "relative flex items-center justify-center mt-2 mb-2",
//                 div { class: "absolute inset-0 flex items-center",
//                     span { class: "w-full border-t" }
//                 }
//                 div { class: "relative bg-background px-2 text-xs uppercase text-muted-foreground",
//                     {t!("auth-or-c")}ontinueWithEmail")}
//                 }
//             }
//         }
//     };

//     rsx!(
//         // Simulate Dialog
//         if props.open {
//             div {
//                 class: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
//                 div {
//                     class: "bg-white p-6 rounded-lg shadow-lg max-w-sm w-full",
//                     div { class: "mb-4",
//                         h2 { class: "text-lg font-bold", {t!("auth-authentication")} }
//                         p { class: "text-sm text-gray-500 mt-2",
//                             {t!("auth-sync-d")}ataPrompt")}
//                         }
//                     }

//                     // Simulate Tabs
//                     div { class: "w-full",
//                         div { class: "grid grid-cols-2 w-full mb-4",
//                             button {
//                                 class: "px-4 py-2 rounded-md {if *selected_tab.get() == \"signup\" {
// \"bg-blue-500 text-white\" } else { \"bg-gray-200\" }}",                                 onclick: move
// |_| selected_tab.set("signup".to_string()),                                 {t!("auth-sign-u")}p")}
//                             }
//                             button {
//                                 class: "px-4 py-2 rounded-md {if *selected_tab.get() == \"signin\" {
// \"bg-blue-500 text-white\" } else { \"bg-gray-200\" }}",                                 onclick: move
// |_| selected_tab.set("signin".to_string()),                                 {t!("auth-sign-i")}n")}
//                             }
//                         }

//                         if *selected_tab.get() == "signup" {
//                             div { class: "space-y-4 mt-4",
//                                 {render_social_buttons.clone()}

//                                 form { onsubmit: handle_email_signup, class: "space-y-4",
//                                     div { class: "space-y-2",
//                                         label { class: "block text-sm font-medium mb-1",
// {t!("auth-email")} }                                         div { class: "relative",
//                                             // Placeholder for Mail icon
//                                             div { class: "absolute left-2 top-2.5 h-4 w-4 text-gray-500",
// "📧" }                                             input {
//                                                 r#type: "email",
//                                                 placeholder: "<EMAIL>",
//                                                 class: "pl-8 w-full border rounded px-3 py-2",
//                                                 value: "{email.get()}",
//                                                 oninput: move |evt| email.set(evt.value.clone()),
//                                                 disabled: *is_loading.get(),
//                                             }
//                                         }
//                                     }

//                                     div { class: "space-y-2",
//                                         label { class: "block text-sm font-medium mb-1",
// {t!("auth-password")} }                                         div { class: "relative",
//                                             // Placeholder for Lock icon
//                                             div { class: "absolute left-2 top-2.5 h-4 w-4 text-gray-500",
// "🔒" }                                             input {
//                                                 r#type: "password",
//                                                 placeholder: "••••••••",
//                                                 class: "pl-8 w-full border rounded px-3 py-2",
//                                                 value: "{password.get()}",
//                                                 oninput: move |evt| password.set(evt.value.clone()),
//                                                 disabled: *is_loading.get(),
//                                             }
//                                         }
//                                     }

//                                     button {
//                                         r#type: "submit",
//                                         class: "w-full px-4 py-2 rounded-md bg-blue-500 text-white
// hover:bg-blue-600",                                         disabled: *is_loading.get(),
//                                         "{if *is_loading.get() { t!(\"auth-signingUp\") } else {
// t!(\"auth-signUpWithEmail\") }}"                                     }
//                                 }
//                             }
//                         }

//                         if *selected_tab.get() == "signin" {
//                             div { class: "space-y-4 mt-4",
//                                 {render_social_buttons.clone()}

//                                 form { onsubmit: handle_email_login, class: "space-y-4",
//                                     div { class: "space-y-2",
//                                         label { class: "block text-sm font-medium mb-1",
// {t!("auth-email")} }                                         div { class: "relative",
//                                             // Placeholder for Mail icon
//                                             div { class: "absolute left-2 top-2.5 h-4 w-4 text-gray-500",
// "📧" }                                             input {
//                                                 r#type: "email",
//                                                 placeholder: "<EMAIL>",
//                                                 class: "pl-8 w-full border rounded px-3 py-2",
//                                                 value: "{email.get()}",
//                                                 oninput: move |evt| email.set(evt.value.clone()),
//                                                 disabled: *is_loading.get(),
//                                             }
//                                         }
//                                     }

//                                     div { class: "space-y-2",
//                                         label { class: "block text-sm font-medium mb-1",
// {t!("auth-password")} }                                         div { class: "relative",
//                                             // Placeholder for Lock icon
//                                             div { class: "absolute left-2 top-2.5 h-4 w-4 text-gray-500",
// "🔒" }                                             input {
//                                                 r#type: "password",
//                                                 placeholder: "••••••••",
//                                                 class: "pl-8 w-full border rounded px-3 py-2",
//                                                 value: "{password.get()}",
//                                                 oninput: move |evt| password.set(evt.value.clone()),
//                                                 disabled: *is_loading.get(),
//                                             }
//                                         }
//                                     }

//                                     button {
//                                         r#type: "submit",
//                                         class: "w-full px-4 py-2 rounded-md bg-blue-500 text-white
// hover:bg-blue-600",                                         disabled: *is_loading.get(),
//                                         "{if *is_loading.get() { t!(\"auth-signingIn\") } else {
// t!(\"auth-signInWithEmail\") }}"                                     }
//                                 }
//                             }
//                         }
//                     }
//                 }
//             }
//         }
//     )
// }

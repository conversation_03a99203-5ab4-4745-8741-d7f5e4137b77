// use dioxus::prelude::*;

// use crate::i18n::*;

// #[derive(<PERSON><PERSON>, <PERSON>ialEq, Clone)]
// pub struct DeleteDogDialogProps {
//     pub is_open:   bool,
//     pub on_close:  EventHandler<()>,
//     #[props(optional)]
//     pub on_delete: Option<EventHandler<()>>,
//     pub dog_name:  String,
//     #[props(optional)]
//     pub dog_id:    Option<String>,
// }

// #[component]
// pub fn DeleteDogDialog(props: DeleteDogDialogProps) -> Element {
//     rsx! {
//         // Simulate AlertDialog
//         if props.is_open {
//             div {
//                 class: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
//                 div {
//                     class: "bg-white p-6 rounded-lg shadow-lg max-w-sm w-full",
//                     div { class: "mb-4",
//                         h2 { class: "text-lg font-bold", {t!("delete-d")}ogDialog-title")} }
//                         p { class: "text-sm text-gray-500 mt-2",
//                             "{t!(\"deleteDogDialog-description\", \"dogName\": props.dog_name.clone())}"
//                         }
//                     }
//                     div { class: "flex justify-end space-x-2",
//                         button {
//                             class: "px-4 py-2 rounded-md border border-gray-300 hover:bg-gray-100",
//                             onclick: move |_| props.on_close.call(()),
//                             {t!("common-cancel")}
//                         }
//                         button {
//                             class: "px-4 py-2 rounded-md bg-red-500 text-white hover:bg-red-600",
//                             onclick: move |_| {
//                                 if let Some(on_delete) = &props.on_delete {
//                                     on_delete.call(());
//                                 }
//                             },
//                             {t!("common-delete")}
//                         }
//                     }
//                 }
//             }
//         }
//     }
// }

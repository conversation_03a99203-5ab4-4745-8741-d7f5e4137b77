use dioxus::prelude::*;

use crate::{
    models::Breed, // Assuming Breed struct is in models
};

#[derive(<PERSON><PERSON>, PartialEq, Clone)]
pub struct BreedItemProps<'a> {
    pub breed:        Breed,
    pub is_selected:  bool,
    pub on_click:     EventHandler<'a, MouseData>,
    #[props(optional)]
    pub display_name: Option<String>,
}

#[component]
pub fn BreedItem<'a>(cx: <PERSON>ope<'a>, props: BreedItemProps<'a>) -> Element {
    rsx!(
        div {
            class: "flex items-center gap-2 p-2 cursor-pointer hover:bg-gray-100 transition-colors rounded-xl {if props.is_selected { \"bg-gray-100\" } else { \"\" }}",
            onclick: move |evt| props.on_click.call(evt),
            div { class: "w-8 h-8 rounded-full overflow-hidden shrink-0 shadow-xs",
                img {
                    src: "{props.breed.image}",
                    alt: "{props.breed.name}",
                    class: "w-full h-full object-cover",
                }
            }
            div { class: "flex-1 min-w-0",
                p { class: "truncate text-sm", "{props.display_name.as_deref().unwrap_or(&props.breed.name)}" }
            }
            if props.is_selected {
                div { class: "w-4 h-4 text-pet-purple", "✔️" } // Placeholder for Check icon
            }
        }
    )
}

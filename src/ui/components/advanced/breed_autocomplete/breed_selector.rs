// use dioxus::prelude::*;

// use crate::{
//     i18n::*,
//     ui::components::BreedAutocomplete, // Placeholder for now
// };

// #[derive(Props, PartialEq, Clone)]
// pub struct BreedSelectorProps {
//     pub selected_breed:    Option<String>,
//     pub on_breed_selected: EventHandler<String>,
//     #[props(optional)]
//     pub photo_url:         Option<String>,
//     #[props(default = true)]
//     pub show_label:        bool,
// }

// #[component]
// pub fn BreedSelector(props: BreedSelectorProps) -> Element {
//     rsx! {
//       div {
//         if props.show_label {
//           label { class: "block text-sm font-medium mb-2", {t!("onboarding-breed-o")}ptional")} }
//         }
//         BreedAutocomplete {
//           value: props.selected_breed.clone(),
//           on_change: move |b: String| props.on_breed_selected.call(b),
//           image_url: props.photo_url.clone(),
//         }
//         if props.show_label {
//           p { class: "text-xs text-gray-500 mt-1", {t!("onboarding-breed-d")}escription")} }
//         }
//       }
//     }
// }

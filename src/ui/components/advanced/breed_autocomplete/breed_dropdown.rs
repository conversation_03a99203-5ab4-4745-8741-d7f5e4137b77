use dioxus::prelude::*;

use crate::{
    i18n::*,
    models::Breed,                                 // Assuming Breed struct is in models
    ui::components::breed_autocomplete::BreedItem, // Placeholder for now
};

#[derive(Props, PartialEq, Clone)]
pub struct BreedDropdownProps<'a> {
    pub breeds:                   Vec<Breed>,
    pub selected_breed:           String,
    pub on_breed_select:          EventHandler<String>,
    pub get_localized_breed_name: &'a dyn Fn(&str) -> String,
}

#[component]
pub fn BreedDropdown<'a>(cx: Scope<'a>, props: BreedDropdownProps<'a>) -> Element {
    let i18n = I18n::new();

    rsx!(
      div { class: "absolute z-50 mt-1 bg-white shadow-lg rounded-md border border-gray-200 overflow-hidden w-full max-h-60 overflow-y-auto",
        if props.breeds.is_empty() {
          div { class: "p-3 text-center text-gray-500", {t!("common-no-r")}esults")} }
        } else {
          div {
            for breed in props.breeds.iter() {
              BreedItem {
                key: "{breed.name}",
                breed: breed.clone(),
                is_selected: breed.name == props.selected_breed,
                on_click: move |_| props.on_breed_select.call(breed.name.clone()),
                display_name: (props.get_localized_breed_name)(&breed.name),
              }
            }
          }
        }
      }
    )
}

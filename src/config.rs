use std::env;

use dotenvy::dotenv;

pub struct Config {
    pub supabase_url:      String,
    pub supabase_anon_key: String,
}

impl Config {
    pub fn from_env() -> Self {
        dotenv().ok(); // Load .env file if it exists

        let supabase_url = env::var("SUPABASE_URL").expect("SUPABASE_URL must be set");
        let supabase_anon_key = env::var("SUPABASE_ANON_KEY").expect("SUPABASE_ANON_KEY must be set");

        Self {
            supabase_url,
            supabase_anon_key,
        }
    }
}

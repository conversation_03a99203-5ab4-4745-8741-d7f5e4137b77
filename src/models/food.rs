use serde::{
    Deserialize,
    Serialize,
};


#[derive(<PERSON><PERSON>, <PERSON>ialEq, Eq, Debug, Deserialize, Serialize)]
pub struct Food {
    pub id:               String,
    pub name:             String,
    pub brand:            String,
    pub calories_per_cup: u32,
    pub is_preferred:     bool,
    pub icon:             String,
    pub logo:             Option<String>,
    pub start_date:       Option<String>, // Using String for simplicity
}

#[derive(<PERSON><PERSON>, PartialEq, Eq, Debug, Deserialize, Serialize)]
pub struct FoodHistoryEntry {
    pub food_id:    String,
    pub name:       String,
    pub brand:      String,
    pub start_date: String,         // Using String for simplicity
    pub end_date:   Option<String>, // Using String for simplicity
}

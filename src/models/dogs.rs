use chrono::{
    DateTime,
    Duration,
    Utc,
};
use serde::{
    Deserialize,
    Serialize,
};

use crate::{
    i18n::t,
    models::{
        activity::{
            Activity,
            ActivityType,
            TypicalActivity,
        },
        food::{
            Food,
            FoodHistoryEntry,
        },
    },
};


#[derive(<PERSON><PERSON>, PartialEq, Eq, Debug)]
pub struct Breed {
    pub id:    String,
    pub name:  String,
    pub image: String,
}

#[derive(Clone, PartialEq, Eq, Debug, Deserialize, Serialize)]
pub struct Dog {
    pub id:                  String,
    pub activities:          Vec<Activity>,
    pub name:                String,
    pub r#type:              Option<String>, // "dog"
    pub breed:               Option<String>,
    pub birthday:            Option<DateTime<Utc>>, /* Using String for simplicity, can be
                                                     * chrono::NaiveDate */
    pub weight:              Option<u32>,
    pub image:               Option<String>,
    pub size:                Option<String>, // "small", "medium", "large"
    pub body_fit_state:      Option<u32>,    // 1-5
    pub daily_calorie_goal:  Option<u32>,
    pub daily_activity_goal: Option<u32>,
    pub activity_progress:   Option<u32>,
    pub calorie_progress:    Option<u32>,
    pub typical_activity:    Option<TypicalActivity>,
    pub preferred_foods:     Vec<Food>,
    pub food_history:        Vec<FoodHistoryEntry>,
    pub last_weight_update:  Option<String>, // Using String for simplicity
}

impl Default for Dog {
    fn default() -> Self {
        Self {
            id:                  "1".to_string(),
            activities:          vec![],
            name:                "Rex".to_string(),
            r#type:              Some("dog".to_string()),
            breed:               Some("Labrador Retriever".to_string()),
            birthday:            Some(
                DateTime::parse_from_rfc3339("2020-01-01T00:00:00Z")
                    .unwrap()
                    .into(),
            ),
            weight:              Some(30),
            image:               Some(
                "https://images.dog.ceo/breeds/labrador/n02099712_315.jpg".to_string(),
            ),
            size:                Some("large".to_string()),
            body_fit_state:      Some(7),
            daily_calorie_goal:  Some(2000),
            daily_activity_goal: Some(30),
            activity_progress:   Some(0),
            calorie_progress:    Some(0),
            typical_activity:    Some(TypicalActivity {
                duration:      30,
                activity_type: Some("walking".to_string()),
            }),
            preferred_foods:     vec![],
            food_history:        vec![],
            last_weight_update:  Some("2020-01-01".to_string()),
        }
    }
}

impl Dog {
    pub fn get_age(&self) -> String {
        self.birthday.as_ref().map_or_else(
            || t!("dog-profile.birthday-not-set"),
            |birthday| {
                let now = chrono::offset::Utc::now();
                let age_days = now.signed_duration_since(birthday).num_days();
                if age_days < 365 {
                    if age_days < 30 {
                        format!("{age_days} days")
                    } else {
                        format!("{} months", (age_days as f64 / 30.4) as i32)
                    }
                } else {
                    format!("{} years", age_days / 365)
                }
            },
        )
    }

    pub fn is_active(&self) -> bool { self.activities.last().is_some_and(|a| a.end_time.is_none()) }

    pub fn get_current_activity_duration(&self) -> Duration {
        self.activities
            .last()
            .map_or_else(Duration::zero, Activity::duration)
    }

    pub fn get_total_activity_duration(&self) -> Duration {
        self.activities
            .iter()
            .map(super::activity::Activity::duration)
            .sum()
    }

    pub fn get_total_activity_duration_today(&self) -> Duration {
        let now = chrono::offset::Utc::now();
        self.activities
            .iter()
            .filter(|a| a.start_time.date() == now.date())
            .map(super::activity::Activity::duration)
            .sum()
    }

    pub fn get_total_activity_duration_this_week(&self) -> Duration {
        let now = chrono::offset::Utc::now();
        self.activities
            .iter()
            .filter(|a| a.start_time.date() >= now.date() - Duration::days(7))
            .map(super::activity::Activity::duration)
            .sum()
    }

    pub fn get_total_activity_duration_this_month(&self) -> Duration {
        let now = chrono::offset::Utc::now();
        self.activities
            .iter()
            .filter(|a| a.start_time.date() >= now.date() - Duration::days(30))
            .map(super::activity::Activity::duration)
            .sum()
    }

    pub fn get_total_activity_duration_this_year(&self) -> Duration {
        let now = chrono::offset::Utc::now();
        self.activities
            .iter()
            .filter(|a| a.start_time.date() >= now.date() - Duration::days(365))
            .map(super::activity::Activity::duration)
            .sum()
    }

    pub fn get_total_activity_duration_all_time(&self) -> Duration {
        self.activities
            .iter()
            .map(super::activity::Activity::duration)
            .sum()
    }

    pub fn get_total_activity_duration_for_period(
        &self,
        start: DateTime<Utc>,
        end: DateTime<Utc>,
    ) -> Duration {
        self.activities
            .iter()
            .filter(|a| a.start_time >= start && a.start_time <= end)
            .map(super::activity::Activity::duration)
            .sum()
    }

    pub fn start_activity(&mut self, activity_type: ActivityType) {
        if self.is_active() {
            return;
        }
        self.activities.push(Activity {
            this_type:  activity_type,
            start_time: chrono::offset::Utc::now(),
            end_time:   None,
        });
    }

    pub fn end_activity(&mut self) {
        if !self.is_active() {
            return;
        }
        self.activities.last_mut().unwrap().end_time = Some(chrono::offset::Utc::now());
    }
}

#[derive(Clone, PartialEq, Eq, Debug)]
pub struct DogRecommendation {
    pub id:       String,
    pub text:     String,
    pub seen:     bool,
    pub category: String,
    // pub created_at: String, // Using String for simplicity
    // pub updated_at: String, // Using String for simplicity
}

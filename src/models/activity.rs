use chrono::{
    DateTime,
    Duration,
    Utc,
};
use serde::{
    Deserialize,
    Serialize,
};

#[derive(<PERSON><PERSON>, PartialEq, Eq, Debug, Deserialize, Serialize)]
pub enum ActivityType {
    Walking,
    Running,
    Mixed,
}

#[derive(Clone, PartialEq, Eq, Debug, Deserialize, Serialize)]
pub struct TypicalActivity {
    pub duration:      u32,
    pub activity_type: Option<String>, // "walking", "running", "mixed"
}

#[derive(Clone, PartialEq, Eq, Debug, Deserialize, Serialize)]
pub struct Activity {
    // pub id:            String,
    pub this_type:  ActivityType,
    pub start_time: DateTime<Utc>,
    pub end_time:   Option<DateTime<Utc>>,
}

impl Activity {
    pub fn duration(&self) -> Duration {
        self.end_time.map_or_else(
            || chrono::offset::Utc::now() - self.start_time,
            |end_time| end_time - self.start_time,
        )
    }
}

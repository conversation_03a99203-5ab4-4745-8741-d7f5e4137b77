use dioxus::{
    prelude::*,
    signals::Signal,
};

use crate::{
    config::Config,
    integrations::supabase::client::SupabaseClient,
    models::UserProfile,
};

pub fn use_auth() -> Signal<Option<UserProfile>> {
    let config = Config::from_env();
    let client = SupabaseClient::new(&config);

    // This is still a mock, but now it's structured to call the client
    let user_profile = use_signal(|| None::<UserProfile>);

    // Example of how you might use the client in an async task
    // use_future(move || async move {
    //     let req = AuthRequest { email: "<EMAIL>", password: "password" };
    //     if let Ok(res) = client.sign_in(&req).await {
    //         // In a real app, you'd decode the token to get user info
    //         user_profile.set(Some(UserProfile {
    //             email: Some("<EMAIL>".to_string()),
    //             // ... fill other fields
    //         }));
    //     }
    // });

    Signal::new(Some(UserProfile {
        anonymous:             false,
        authenticated:         true,
        email:                 Some("<EMAIL>".to_string()),
        provider:              Some("email".to_string()),
        email_confirmed_at:    None,
        notifications_enabled: true,
        unit_system:           "metric".to_string(),
    }))
}

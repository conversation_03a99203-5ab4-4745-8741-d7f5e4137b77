use dioxus::{
    prelude::*,
    signals::Signal,
};

use crate::models::WeightLog;

// Mock implementation of use_weight_log_data hook
pub fn use_weight_log_data() -> Signal<Vec<WeightLog>> {
    // In a real app, you would fetch this from a database or local storage.
    // For now, we return a static list of weight logs.
    Signal::new(vec![
        WeightLog {
            id:      "log1".to_string(),
            pet_id:  "1".to_string(),
            date:    "2024-07-01".to_string(),
            weight:  30,
            comment: None,
        },
        WeightLog {
            id:      "log2".to_string(),
            pet_id:  "1".to_string(),
            date:    "2024-07-08".to_string(),
            weight:  30,
            comment: Some("Looking good!".to_string()),
        },
    ])
}

// use dioxus::prelude::*;
// use uuid::Uuid;

// use crate::{
//     utils::local_storage_helpers::{
//         get_meals,
//         save_meals,
//     },
//     models::Meal,
// };

// #[derive(Clone, PartialEq)]
// pub struct MealData {
//     pub meals:     Vec<Meal>,
//     pub is_loaded: bool,
// }

// #[derive(Props, PartialEq, Clone)]
// pub struct UseMealDataProps {}

// #[component]
// pub fn use_meal_data(props: UseMealDataProps) -> MealData {
//     let meals = use_signal(|| Vec::<Meal>::new());
//     let is_loaded = use_signal(|| false);

//     // Load data from localStorage on component mount
//     use_effect(move || {
//         meals.set(get_meals());
//         is_loaded.set(true);
//         async {}
//     });

//     // Meal CRUD operations
//     let add_meal = move |meal_data: Meal| {
//         let new_meal = Meal {
//             id: Uuid::new_v4().to_string(),
//             ..meal_data
//         };
//         let mut current_meals = meals.get().clone();
//         current_meals.push(new_meal.clone());
//         meals.set(current_meals);
//         save_meals(meals.get());
//         new_meal
//     };

//     let update_meal = move |updated_meal: Meal| {
//         let updated_meals: Vec<Meal> = meals
//             .iter()
//             .map(|meal| {
//                 if meal.id == updated_meal.id {
//                     updated_meal.clone()
//                 } else {
//                     meal.clone()
//                 }
//             })
//             .collect();
//         meals.set(updated_meals);
//         save_meals(meals.get());
//     };

//     let delete_meal = move |id: String| {
//         let updated_meals: Vec<Meal> = meals.iter().filter(|meal| meal.id != id).cloned().collect();
//         meals.set(updated_meals);
//         save_meals(meals.get());
//     };

//     let get_meals_by_pet_id = move |pet_id: String| -> Vec<Meal> {
//         meals
//             .iter()
//             .filter(|meal| meal.pet_id == pet_id)
//             .cloned()
//             .collect()
//     };

//     MealData {
//         meals: meals.get().clone(),
//         add_meal,
//         update_meal,
//         delete_meal,
//         get_meals_by_pet_id,
//         is_loaded: *is_loaded.get(),
//     }
// }

// use dioxus::prelude::*;
// use uuid::Uuid;

// use crate::{
//     utils::local_storage_helpers::{
//         get_activities,
//         save_activities,
//     },
//     models::Activity,
// };

// #[derive(Clone, PartialEq)]
// pub struct ActivityData {
//     pub activities: Vec<Activity>,
//     pub is_loaded:  bool,
// }

// #[derive(Props, PartialEq, Clone)]
// pub struct UseActivityDataProps {}

// #[component]
// pub fn use_activity_data(props: UseActivityDataProps) -> ActivityData {
//     let activities = use_signal(|| Vec::<Activity>::new());
//     let is_loaded = use_signal(|| false);

//     // Load data from localStorage on component mount
//     use_effect(move || {
//         let loaded_activities = get_activities();
//         activities.set(loaded_activities);
//         is_loaded.set(true);
//         async {}
//     });

//     // Activity CRUD operations
//     let add_activity = move |activity_data: Activity| {
//         let new_activity = Activity {
//             id: Uuid::new_v4().to_string(),
//             ..activity_data
//         };
//         let mut current_activities = activities.get().clone();
//         current_activities.push(new_activity.clone());
//         activities.set(current_activities);
//         save_activities(activities.get());
//         new_activity
//     };

//     let update_activity = move |updated_activity: Activity| {
//         let updated_activities: Vec<Activity> = activities
//             .iter()
//             .map(|activity| {
//                 if activity.id == updated_activity.id {
//                     updated_activity.clone()
//                 } else {
//                     activity.clone()
//                 }
//             })
//             .collect();
//         activities.set(updated_activities);
//         save_activities(activities.get());
//     };

//     let delete_activity = move |id: String| {
//         let updated_activities: Vec<Activity> = activities
//             .iter()
//             .filter(|activity| activity.id != id)
//             .cloned()
//             .collect();
//         activities.set(updated_activities);
//         save_activities(activities.get());
//     };

//     let get_activities_by_pet_id = move |pet_id: String| -> Vec<Activity> {
//         activities
//             .iter()
//             .filter(|activity| activity.pet_id == pet_id)
//             .cloned()
//             .collect()
//     };

//     ActivityData {
//         activities: activities.get().clone(),
//         add_activity,
//         update_activity,
//         delete_activity,
//         get_activities_by_pet_id,
//         is_loaded: *is_loaded.get(),
//     }
// }

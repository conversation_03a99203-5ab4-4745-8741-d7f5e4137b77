// use dioxus::prelude::*;

// use crate::{
//     models::DogRecommendation,
//     utils::local_storage_helpers::{
//         get_recommendations,
//         save_recommendations,
//     },
// };

// #[derive(<PERSON><PERSON>, PartialEq)]
// pub struct RecommendationData {
//     pub recommendations: Vec<DogRecommendation>,
//     pub is_loaded:       bool,
// }

// #[derive(Props, PartialEq, Clone)]
// pub struct UseRecommendationDataProps {}

// #[component]
// pub fn use_recommendation_data(props: UseRecommendationDataProps) -> RecommendationData {
//     let recommendations = use_signal(|| Vec::<DogRecommendation>::new());
//     let is_loaded = use_signal(|| false);

//     // Load data from localStorage on component mount
//     use_effect(move || {
//         recommendations.set(get_recommendations());
//         is_loaded.set(true);
//         async {}
//     });

//     RecommendationData {
//         recommendations: recommendations.get().clone(),
//         is_loaded:       *is_loaded.get(),
//     }
// }

use postgrest::Postgrest;

use crate::config::Config;

pub struct SupabaseClient {
    client: Postgrest,
}

impl SupabaseClient {
    pub fn new(config: &Config) -> Self {
        let client =
            Postgrest::new(&config.supabase_url).insert_header("apikey", &config.supabase_anon_key);

        Self {
            client,
        }
    }

    // We will add methods for auth and db operations here later
}

use dioxus::logger::tracing::{
    error,
    info,
};
use gloo_storage::{
    LocalStorage,
    Storage,
};
use serde::{
    Deserialize,
    Serialize,
};
use serde_json;

pub fn save_item<T: Serialize>(key: &str, item: &T) {
    match serde_json::to_string(item) {
        Ok(serialized_item) =>
            if let Err(e) = LocalStorage::set(key, &serialized_item) {
                error!("Error saving item to local storage: {:?}", e);
            } else {
                info!("Successfully saved item '{}' to local storage.", key);
            },
        Err(e) => error!("Error serializing item: {:?}", e),
    }
}

pub fn get_item<T: for<'de> Deserialize<'de>>(key: &str) -> Option<T> {
    let key = key.to_string();
    match LocalStorage::get::<String>(key.as_str()) {
        Ok(serialized_item) => match serde_json::from_str(&serialized_item) {
            Ok(item) => Some(item),
            Err(e) => {
                error!("Error deserializing item '{}': {:?}", key, e);
                None
            },
        },
        Err(e) => {
            info!("Item '{}' not found in local storage or error occurred: {:?}", key, e);
            None
        },
    }
}

pub fn remove_item(key: &str) {
    LocalStorage::delete(key);
    info!("Successfully removed item '{}' from local storage.", key);
}

// Specific helpers for Pet, Activity, Meal, WeightLog, DogRecommendation, UserProfile
// use crate::models::{
//     Activity,
//     Dog,
//     DogRecommendation,
//     Meal,
//     UserProfile,
//     WeightLog,
// };

pub const APP_STATE_KEY: &str = "app-state";
const PETS_KEY: &str = "pets";
const ACTIVITIES_KEY: &str = "activities";
const MEALS_KEY: &str = "meals";
const WEIGHT_LOGS_KEY: &str = "weight-logs";
const RECOMMENDATIONS_KEY: &str = "recommendations";
const SELECTED_PET_ID_KEY: &str = "selected-pet-id";
const USER_PROFILE_KEY: &str = "user-profile";
const ONBOARDING_COMPLETED_KEY: &str = "onboarding-completed";
const WELCOME_NOTIFICATION_SHOWN_KEY: &str = "welcome-notification-shown";

// pub fn get_dogs() -> Vec<Dog> { get_item(PETS_KEY).unwrap_or_else(Vec::new) }

// pub fn save_dogs(dogs: &[Dog]) { save_item(PETS_KEY, dogs); }

// pub fn get_activities() -> Vec<Activity> { get_item(ACTIVITIES_KEY).unwrap_or_else(Vec::new) }

// pub fn save_activities(activities: &[Activity]) { save_item(ACTIVITIES_KEY, activities); }

// pub fn get_meals() -> Vec<Meal> { get_item(MEALS_KEY).unwrap_or_else(Vec::new) }

// pub fn save_meals(meals: &[Meal]) { save_item(MEALS_KEY, meals); }

// pub fn get_weight_logs() -> Vec<WeightLog> { get_item(WEIGHT_LOGS_KEY).unwrap_or_else(Vec::new) }

// pub fn save_weight_logs(weight_logs: &[WeightLog]) { save_item(WEIGHT_LOGS_KEY, weight_logs); }

// pub fn get_recommendations() -> Vec<DogRecommendation> {
//     get_item(RECOMMENDATIONS_KEY).unwrap_or_else(Vec::new)
// }

// pub fn save_recommendations(recommendations: &[DogRecommendation]) {
//     save_item(RECOMMENDATIONS_KEY, recommendations);
// }

// pub fn get_selected_pet_id() -> Option<String> { get_item(SELECTED_PET_ID_KEY) }

// pub fn save_selected_pet_id(id: &str) { save_item(SELECTED_PET_ID_KEY, &id.to_string()); }

// pub fn remove_selected_pet_id() { remove_item(SELECTED_PET_ID_KEY); }

// pub fn get_user_profile() -> Option<UserProfile> { get_item(USER_PROFILE_KEY) }

// pub fn save_user_profile(profile: &UserProfile) { save_item(USER_PROFILE_KEY, profile); }

// pub fn get_onboarding_completed() -> bool { get_item(ONBOARDING_COMPLETED_KEY).unwrap_or(false) }

// pub fn set_onboarding_completed(completed: bool) { save_item(ONBOARDING_COMPLETED_KEY, &completed); }

// pub fn get_welcome_notification_shown() -> bool {
//     get_item(WELCOME_NOTIFICATION_SHOWN_KEY).unwrap_or(false)
// }

// pub fn set_welcome_notification_shown(shown: bool) { save_item(WELCOME_NOTIFICATION_SHOWN_KEY, &shown); }

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1007 197"><style>
@keyframes a0_t { 0% { transform: translate(225px,339.9px) scale(0,1) translate(-89.5px,-24px); } 35% { transform: translate(225px,339.9px) scale(0,1) translate(-89.5px,-24px); animation-timing-function: cubic-bezier(.6,0,.4,1); } 45% { transform: translate(225px,339.9px) scale(1,1) translate(-89.5px,-24px); } 50% { transform: translate(225px,339.9px) scale(1,1) translate(-89.5px,-24px); animation-timing-function: cubic-bezier(.6,0,.4,1); } 60% { transform: translate(225px,339.9px) scale(0,1) translate(-89.5px,-24px); } 100% { transform: translate(225px,339.9px) scale(0,1) translate(-89.5px,-24px); } }
@keyframes a1_t { 0% { transform: translate(225px,348.9px) scale(0,1) translate(-41.5px,-11px); } 20% { transform: translate(225px,348.9px) scale(0,1) translate(-41.5px,-11px); animation-timing-function: cubic-bezier(.6,0,.4,1); } 30% { transform: translate(225px,348.9px) scale(1,1) translate(-41.5px,-11px); } 35% { transform: translate(225px,348.9px) scale(1,1) translate(-41.5px,-11px); } 45% { transform: translate(225px,348.9px) scale(0,1) translate(-41.5px,-11px); } 100% { transform: translate(225px,348.9px) scale(0,1) translate(-41.5px,-11px); } }
@keyframes a3_t { 0% { transform: translate(225px,198.9px) rotate(-90deg); } 10% { transform: translate(225px,198.9px) rotate(-90deg); } 20% { transform: translate(225px,198.9px) rotate(-90deg); animation-timing-function: cubic-bezier(.6,0,.4,1); } 30% { transform: translate(225px,198.9px) rotate(0deg); } 35% { transform: translate(225px,198.9px) rotate(0deg); } 45% { transform: translate(225px,198.9px) rotate(0deg); } 50% { transform: translate(225px,198.9px) rotate(0deg); animation-timing-function: cubic-bezier(.6,0,.4,1); } 60% { transform: translate(225px,198.9px) rotate(-90deg); } 65% { transform: translate(225px,198.9px) rotate(-90deg); } 75% { transform: translate(225px,198.9px) rotate(-90deg); } 100% { transform: translate(225px,198.9px) rotate(-90deg); } }
@keyframes a2_t { 0% { transform: translate(-200px,-167px); animation-timing-function: cubic-bezier(.6,0,.4,1); } 10% { transform: translate(-150px,-80px); } 20% { transform: translate(-150px,-80px); animation-timing-function: cubic-bezier(.6,0,.4,1); } 30% { transform: translate(-200px,-123px); } 35% { transform: translate(-200px,-123px); animation-timing-function: cubic-bezier(.6,0,.4,1); } 45% { transform: translate(-150px,-100px); } 50% { transform: translate(-150px,-100px); animation-timing-function: cubic-bezier(.6,0,.4,1); } 60% { transform: translate(-150px,-80px); } 65% { transform: translate(-150px,-80px); animation-timing-function: cubic-bezier(.6,0,.4,1); } 75% { transform: translate(-217px,-200px); } 100% { transform: translate(-217px,-200px); } }
@keyframes a2_o { 0% { opacity: 0; animation-timing-function: cubic-bezier(.6,0,.4,1); } 10% { opacity: 1; } 65% { opacity: 1; animation-timing-function: cubic-bezier(.6,0,.4,1); } 75% { opacity: 0; } 100% { opacity: 0; } }
@keyframes a2_sw { 0% { stroke-width: 30px; animation-timing-function: cubic-bezier(.6,0,.4,1); } 10% { stroke-width: 8px; } 65% { stroke-width: 8px; animation-timing-function: cubic-bezier(.6,0,.4,1); } 75% { stroke-width: 30px; } 100% { stroke-width: 30px; } }
@keyframes a2_w { 0% { width: 400px; animation-timing-function: cubic-bezier(.6,0,.4,1); } 10% { width: 300px; } 20% { width: 300px; animation-timing-function: cubic-bezier(.6,0,.4,1); } 30% { width: 400px; } 35% { width: 400px; animation-timing-function: cubic-bezier(.6,0,.4,1); } 45% { width: 300px; } 50% { width: 300px; } 60% { width: 300px; } 65% { width: 300px; animation-timing-function: cubic-bezier(.6,0,.4,1); } 75% { width: 434px; } 100% { width: 434px; } }
@keyframes a2_h { 0% { height: 334px; animation-timing-function: cubic-bezier(.6,0,.4,1); } 10% { height: 160px; } 20% { height: 160px; animation-timing-function: cubic-bezier(.6,0,.4,1); } 30% { height: 246px; } 35% { height: 246px; animation-timing-function: cubic-bezier(.6,0,.4,1); } 45% { height: 200px; } 50% { height: 200px; animation-timing-function: cubic-bezier(.6,0,.4,1); } 60% { height: 160px; } 65% { height: 160px; animation-timing-function: cubic-bezier(.6,0,.4,1); } 75% { height: 400px; } 100% { height: 400px; } }
@keyframes a4_t { 0% { transform: translate(143.5px,48.9px); } 10% { transform: translate(180.5px,98.9px); } 100% { transform: translate(180.5px,98.9px); } }
@keyframes a4_w { 0% { width: 168.5px; } 10% { width: 94.5px; } 100% { width: 94.5px; } }
@keyframes a4_h { 0% { height: 302px; } 10% { height: 202px; } 100% { height: 202px; } }
@keyframes a5_t { 0% { transform: translate(217px,324.9px); animation-timing-function: cubic-bezier(.6,0,.4,1); } 10% { transform: translate(190px,324.9px); } 20% { transform: translate(190px,324.9px); } 30% { transform: translate(190px,324.9px); } 100% { transform: translate(190px,324.9px); } }
@keyframes a5_o { 0% { opacity: 0; animation-timing-function: cubic-bezier(.6,0,.4,1); } 10% { opacity: 1; } 20% { opacity: 1; } 30% { opacity: 0; } 100% { opacity: 0; } }
@keyframes a5_w { 0% { width: 16px; animation-timing-function: cubic-bezier(.6,0,.4,1); } 10% { width: 70px; } 20% { width: 70px; } 30% { width: 70px; } 100% { width: 70px; } }
@keyframes a6_t { 0% { transform: translate(217px,324.9px); } 50% { transform: translate(217px,324.9px); animation-timing-function: cubic-bezier(.6,0,.4,1); } 60% { transform: translate(190px,324.9px); } 65% { transform: translate(190px,324.9px); } 75% { transform: translate(190px,324.9px); } 100% { transform: translate(190px,324.9px); } }
@keyframes a6_o { 0% { opacity: 0; } 50% { opacity: 0; animation-timing-function: cubic-bezier(.6,0,.4,1); } 60% { opacity: 1; } 65% { opacity: 1; animation-timing-function: cubic-bezier(.6,0,.4,1); } 75% { opacity: 0; } 100% { opacity: 0; } }
@keyframes a6_w { 0% { width: 16px; } 50% { width: 16px; animation-timing-function: cubic-bezier(.6,0,.4,1); } 60% { width: 70px; } 65% { width: 70px; } 75% { width: 70px; } 100% { width: 70px; } }
@keyframes a7_t { 0% { transform: translate(225px,198.9px) scale(1.5,1.5) translate(-42.3px,-93px); animation-timing-function: cubic-bezier(.6,0,.4,1); } 10% { transform: translate(225px,198.9px) scale(1,1) translate(-42.3px,-93px); } 20% { transform: translate(225px,198.9px) scale(1,1) translate(-42.3px,-93px); animation-timing-function: cubic-bezier(.6,0,.4,1); } 25% { transform: translate(225px,198.9px) scale(.8,.8) translate(-42.3px,-93px); animation-timing-function: cubic-bezier(.6,0,.4,1); } 30% { transform: translate(225px,198.9px) scale(1,1) translate(-42.3px,-93px); } 35% { transform: translate(225px,198.9px) scale(1,1) translate(-42.3px,-93px); animation-timing-function: cubic-bezier(.4,0,.6,1); } 45% { transform: translate(225px,198.9px) scale(.8,.8) translate(-42.3px,-93px); } 50% { transform: translate(225px,198.9px) scale(.8,.8) translate(-42.3px,-93px); animation-timing-function: cubic-bezier(.4,0,.6,1); } 55% { transform: translate(225px,198.9px) scale(.7,.7) translate(-42.3px,-93px); animation-timing-function: cubic-bezier(.4,0,.6,1); } 60% { transform: translate(225px,198.9px) scale(1,1) translate(-42.3px,-93px); } 65% { transform: translate(225px,198.9px) scale(1,1) translate(-42.3px,-93px); animation-timing-function: cubic-bezier(.4,0,.6,1); } 75% { transform: translate(225px,198.9px) scale(1.5,1.5) translate(-42.3px,-93px); } 100% { transform: translate(225px,198.9px) scale(1.5,1.5) translate(-42.3px,-93px); } }
</style><defs><symbol id="Symbol-2" preserveAspectRatio="none" width="111.5" height="186" viewBox="0 0 111.5 186" overflow="visible"><g transform="translate(8.5,4.5)"><rect x="4" width="85" height="8" fill="#d9d9d9"/><rect x="4" y="26" width="85" height="8" fill="#d9d9d9"/><rect x="25" y="56" width="43" height="8" fill="#d9d9d9"/><rect width="85" height="8" fill="#d9d9d9" transform="translate(46.5,175) rotate(180) scale(-1,1) translate(-42.5,-4)"/><rect width="85" height="8" fill="#d9d9d9" transform="translate(46.5,149) rotate(180) scale(-1,1) translate(-42.5,-4)"/><rect width="43" height="8" fill="#d9d9d9" transform="translate(46.5,119) rotate(180) scale(-1,1) translate(-21.5,-4)"/><path d="M0 4c0 53 94.5 115 94.5 169" stroke="#3cc4dc" stroke-width="17" stroke-linecap="square" fill="none"/><path d="M94.5 4c0 53-94.5 115-94.5 169" stroke="#fb422d" stroke-width="17" stroke-linecap="square" fill="none"/></g></symbol><symbol id="Symbol-5" preserveAspectRatio="none" width="84.5" height="186" viewBox="0 0 84.5 186" overflow="visible"><g transform="translate(42.3,93) scale(5.352,6.9) translate(-16,60)"><g transform="translate(-34.9,-125.8)"><g transform="translate(24,5)"><path d="M33 46.5c0 3.1-0.8 5.5-2.2 7.4c-1.4 1.9-3.3 3.6-5.4 5.4c-2.2 1.7-4.5 3.6-6.3 6.2c-1.8 2.5-3.1 5.5-3.1 9.5h4.7c0-3.1 .9-5.4 2.2-7.3c1.4-1.9 3.3-3.5 5.5-5.3c2.1-1.8 4.4-3.7 6.2-6.2c1.8-2.6 3.1-5.6 3.1-9.7Z" fill="#e96020"/><path d="M20.4 70.7c-0.6 0-1 .5-1 1.1c0 .7 .4 1.2 1 1.2h12.9c.7 0 1.2-0.5 1.2-1.2c0-0.6-0.5-1.1-1.2-1.1Z" fill="#2d323b"/><path d="M21.8 66.6c-0.7 0-1 .6-1 1.2c0 .6 .3 1 .9 1h10.3c.6 0 1.1-0.5 1.1-1.1c0-0.6-0.5-1.1-1.1-1.1Z" fill="#2d323b"/><path d="M21.8 53c-0.7 0-1.2 .5-1.1 1.1c0 .6 .4 1.1 1.1 1.1h10.2c.6 0 .9-0.6 .9-1.2c0-0.6-0.3-1-0.9-1Z" fill="#2d323b"/><path d="M20.4 48.8c-0.6 0-1.2 .5-1.2 1.1c0 .7 .6 1.2 1.2 1.2h12.9c.7 0 1-0.5 1-1.1c0-0.7-0.3-1.2-1-1.2Z" fill="#2d323b"/><path d="M16 46.5c0 4.1 1.3 7.1 3.1 9.7c1.8 2.5 4.1 4.4 6.3 6.2c2.1 1.8 4 3.6 5.4 5.5c1.4 1.9 2.2 4 2.2 7.1h4.7c0-4-1.3-7.1-3.1-9.6c-1.8-2.5-4.1-4.4-6.2-6.2c-2.2-1.8-4.1-3.4-5.5-5.3c-1.3-1.9-2.2-4.3-2.2-7.4Z" fill="#00a8d6"/></g></g></g></symbol></defs><rect width="1007" height="197" stroke="#0f1116" fill="#0f1116" stroke-width="0"/><path fill="#fff" d="M83.3 106.1v-65.7h17c4.2 0 7.9 .8 11 2.4c3.1 1.6 5.5 3.9 7.2 6.9c1.7 2.9 2.6 6.4 2.6 10.4v26.2c0 4-0.9 7.5-2.6 10.5c-1.7 3-4.1 5.3-7.2 6.9c-3.1 1.6-6.8 2.4-11 2.4Zm8.1-7.2h8.9c4 0 7.1-1.1 9.3-3.3c2.3-2.3 3.4-5.3 3.4-9.3v-26.2c0-3.9-1.1-7-3.4-9.2c-2.2-2.2-5.3-3.3-9.3-3.3h-8.9Zm45.3 7.2v-7.4h17.5v-34.7h-15.7v-7.4h23.8v42.1h16.7v7.4Zm20.7-58.4c-2 0-3.6-0.5-4.7-1.5c-1.2-1.1-1.7-2.4-1.7-4.2c0-1.8 .5-3.2 1.7-4.3c1.1-1 2.7-1.5 4.7-1.5c1.9 0 3.5 .5 4.6 1.5c1.2 1.1 1.7 2.5 1.7 4.3c0 1.8-0.5 3.1-1.7 4.2c-1.1 1-2.7 1.5-4.6 1.5Zm52.6 59.1c-6 0-10.7-1.7-14.2-5.1c-3.4-3.4-5.1-8-5.1-13.8v-13.1c0-5.9 1.7-10.5 5.1-13.9c3.5-3.3 8.2-5 14.2-5c6 0 10.7 1.7 14.2 5c3.4 3.4 5.2 8 5.2 13.8v13.2c0 5.8-1.8 10.4-5.2 13.8c-3.5 3.4-8.2 5.1-14.2 5.1Zm0-7.2c3.5 0 6.3-1 8.3-3c2-1.9 3-4.8 3-8.7v-13.1c0-3.9-1-6.8-3-8.8c-2-1.9-4.8-2.9-8.3-2.9c-3.5 0-6.2 1-8.2 2.9c-2 2-3 4.9-3 8.8v13.1c0 3.9 1 6.8 3 8.7c2 2 4.7 3 8.2 3Zm31.5 6.5l17.6-25.5l-16.5-24h9.5l9.8 15.3c.5 .7 .9 1.4 1.2 2.2c.4 .7 .8 1.4 1 1.8c.2-0.4 .5-1.1 .8-1.8c.4-0.8 .8-1.5 1.3-2.2l9.9-15.3h9.4l-16.5 24.1l17.5 25.4h-9.5l-10.7-16.2c-0.4-0.7-0.8-1.4-1.2-2.3c-0.4-0.9-0.7-1.6-1-2.1c-0.2 .5-0.6 1.2-1 2.1c-0.5 .9-1 1.6-1.4 2.3l-10.7 16.2Zm76.4 .9c-5.7 0-10.2-1.7-13.7-5.1c-3.4-3.4-5.1-8-5.1-13.8v-31.5h8.1v31.5c0 3.7 1 6.6 2.9 8.7c1.9 2.1 4.5 3.1 7.8 3.1c3.4 0 6-1 8-3.1c1.9-2.1 2.9-5 2.9-8.7v-31.5h8.1v31.5c0 5.8-1.7 10.4-5.2 13.8c-3.5 3.4-8.1 5.1-13.8 5.1Zm52.6-0.2c-5.1 0-9.3-1.2-12.5-3.5c-3.3-2.3-4.9-5.5-4.9-9.6h8.3c0 1.8 .9 3.3 2.6 4.3c1.8 1.1 4 1.6 6.6 1.6h3.8c3.2 0 5.6-0.6 7.1-1.9c1.6-1.3 2.4-3 2.4-5.2c0-2.1-0.8-3.8-2.2-5c-1.5-1.2-3.6-2-6.4-2.5l-6.6-1c-5-0.9-8.6-2.4-10.9-4.7c-2.4-2.3-3.5-5.5-3.5-9.5c0-4.5 1.4-7.9 4.3-10.3c2.9-2.4 7.1-3.6 12.6-3.6h3.4c5 0 9.1 1.1 12.1 3.4c3 2.2 4.6 5.2 4.6 8.9h-8.3c0-1.6-0.8-2.8-2.3-3.7c-1.5-1-3.6-1.4-6.3-1.4h-3.4c-2.8 0-5 .6-6.5 1.7c-1.5 1.2-2.3 2.9-2.3 5c0 3.6 2.5 5.8 7.6 6.7l6.7 1.1c5.3 .8 9.2 2.4 11.7 4.6c2.4 2.3 3.6 5.6 3.6 9.9c0 4.6-1.5 8.2-4.4 10.8c-2.9 2.6-7.3 3.9-13.2 3.9Z"/><path fill="#fff" d="M91 164c-2 0-3.6-0.6-4.7-1.7c-1.1-1.1-1.6-2.7-1.6-4.6v-15.9h2.7v15.9c0 1.2 .3 2.2 .9 2.8c.6 .7 1.5 1.1 2.7 1.1c1.2 0 2.1-0.4 2.7-1.1c.6-0.7 .9-1.6 .9-2.8v-15.9h2.7v15.9c0 2-0.5 3.5-1.6 4.6c-1.1 1.1-2.6 1.7-4.7 1.7Zm17.5-0.1c-1.7 0-3.1-0.3-4.2-1.1c-1.1-0.8-1.6-1.9-1.6-3.2h2.8c0 .6 .3 1.1 .8 1.4c.6 .4 1.4 .5 2.2 .5h1.3c1.1 0 1.9-0.2 2.4-0.6c.5-0.4 .8-1 .8-1.7c0-0.7-0.3-1.3-0.8-1.7c-0.5-0.4-1.2-0.7-2.1-0.8l-2.2-0.4c-1.7-0.3-2.9-0.8-3.6-1.5c-0.8-0.8-1.2-1.9-1.2-3.2c0-1.5 .5-2.6 1.4-3.4c1-0.8 2.4-1.2 4.2-1.2h1.2c1.6 0 3 .3 4 1.1c1 .7 1.5 1.7 1.5 3h-2.7c0-0.6-0.3-1-0.8-1.3c-0.5-0.3-1.2-0.4-2.1-0.4h-1.1c-1 0-1.7 .2-2.2 .5c-0.5 .4-0.8 1-0.8 1.7c0 1.2 .9 1.9 2.6 2.2l2.2 .4c1.8 .3 3.1 .8 3.9 1.5c.8 .8 1.2 1.9 1.2 3.3c0 1.6-0.5 2.8-1.5 3.6c-0.9 .9-2.4 1.3-4.4 1.3Zm18.5 .1c-1.3 0-2.4-0.3-3.4-0.8c-1-0.5-1.7-1.2-2.3-2.2c-0.5-0.9-0.8-2-0.8-3.3v-4.5c0-1.3 .3-2.4 .8-3.3c.6-1 1.3-1.7 2.3-2.2c1-0.5 2.1-0.8 3.4-0.8c1.3 0 2.4 .3 3.4 .8c1 .5 1.7 1.2 2.3 2.2c.5 .9 .8 2 .8 3.3l-0.1 2.9h-10.2v1.6c0 1.3 .3 2.3 1 3c.6 .7 1.6 1 2.8 1c1 0 1.9-0.2 2.5-0.5c.7-0.4 1-1 1.2-1.7h2.7c-0.2 1.4-0.9 2.5-2 3.3c-1.2 .8-2.7 1.2-4.4 1.2Zm-3.8-9.9h7.6v-0.9c0-1.3-0.3-2.3-1-3c-0.6-0.7-1.6-1-2.8-1c-1.2 0-2.2 .3-2.8 1c-0.7 .7-1 1.7-1 3Zm16.1 9.6v-16.5h2.7v3.2c.1-1.1 .6-2 1.4-2.6c.7-0.6 1.7-0.9 3-0.9c1.7 0 3.1 .5 4.1 1.6c1 1.1 1.5 2.6 1.5 4.5v1.1h-2.7v-0.9c0-1.3-0.3-2.2-0.9-2.9c-0.7-0.7-1.6-1.1-2.8-1.1c-2.4 0-3.6 1.4-3.6 4v10.5Zm35.8 0v-2.5h4.5v-16.9h-4.5v-2.5h11.8l-0.1 2.5h-4.4v16.9h4.4v2.5Zm17.7 0v-16.5h2.7v3.2c.1-1.1 .6-2 1.3-2.6c.8-0.6 1.8-0.9 3.1-0.9c1.6 0 3 .5 3.9 1.5c1 1.1 1.5 2.4 1.5 4.2v11.1h-2.7v-10.8c0-1.2-0.3-2.1-0.9-2.7c-0.7-0.6-1.5-1-2.6-1c-1.1 0-2 .4-2.7 1.1c-0.6 .6-0.9 1.6-0.9 2.9v10.5Zm26 0c-1.4 0-2.4-0.4-3.3-1.2c-0.8-0.8-1.2-1.8-1.2-3.1v-9.7h-4.7v-2.5h4.7v-4.7h2.7v4.7h6.6v2.5h-6.6v9.7c0 .5 .2 1 .5 1.3c.3 .4 .8 .5 1.3 .5h4.5v2.5Zm16.2 .3c-1.3 0-2.4-0.3-3.4-0.8c-1-0.5-1.7-1.2-2.3-2.2c-0.5-0.9-0.7-2-0.7-3.3v-4.5c0-1.3 .2-2.4 .7-3.3c.6-1 1.3-1.7 2.3-2.2c1-0.5 2.1-0.8 3.4-0.8c1.3 0 2.4 .3 3.4 .8c1 .5 1.7 1.2 2.3 2.2c.5 .9 .8 2 .8 3.3l-0.1 2.9h-10.2v1.6c0 1.3 .3 2.3 1 3c.6 .7 1.6 1 2.8 1c1 0 1.9-0.2 2.5-0.5c.7-0.4 1-1 1.2-1.7h2.7c-0.2 1.4-0.9 2.5-2 3.3c-1.2 .8-2.7 1.2-4.4 1.2Zm-3.8-9.9h7.6v-0.9c0-1.3-0.3-2.3-1-3c-0.6-0.7-1.6-1-2.8-1c-1.2 0-2.2 .3-2.8 1c-0.7 .7-1 1.7-1 3Zm16.1 9.6v-16.5h2.7v3.2c.1-1.1 .6-2 1.4-2.6c.7-0.6 1.7-0.9 3-0.9c1.7 0 3.1 .5 4.1 1.6c1 1.1 1.5 2.6 1.5 4.5v1.1h-2.7v-0.9c0-1.3-0.3-2.2-0.9-2.9c-0.7-0.7-1.6-1.1-2.8-1.1c-2.4 0-3.6 1.4-3.6 4v10.5Zm21.3 0v-12.4h-4.8v-2.4h4.8v-2.9c0-1.3 .4-2.3 1.2-3.1c.9-0.7 2-1.1 3.4-1.1h4.9v2.5h-4.9c-1.3 0-1.9 .5-1.9 1.7v2.9h6.8v2.4h-6.8v12.4Zm19 .3c-1.8 0-3.1-0.4-4.1-1.3c-1-0.9-1.5-2.1-1.5-3.6c0-1.6 .5-2.8 1.5-3.7c1-0.9 2.3-1.3 4-1.3h5.1v-1.6c0-1-0.3-1.8-0.9-2.4c-0.6-0.5-1.5-0.8-2.5-0.8c-1 0-1.8 .2-2.5 .6c-0.6 .4-1 1-1.2 1.7h-2.7c.1-1 .4-1.8 1-2.5c.6-0.7 1.3-1.2 2.2-1.6c1-0.4 2-0.6 3.2-0.6c1.9 0 3.4 .5 4.5 1.5c1.1 1 1.6 2.3 1.6 4v11.3h-2.6v-3.1h-0.1c-0.1 1-0.6 1.9-1.5 2.5c-0.9 .6-2.1 .9-3.5 .9Zm.6-2.1c1.3 0 2.4-0.3 3.2-1c.8-0.6 1.2-1.4 1.2-2.4v-2.4h-5c-0.9 0-1.6 .3-2.2 .8c-0.5 .5-0.7 1.1-0.7 2c0 .9 .3 1.6 .9 2.2c.6 .5 1.5 .8 2.6 .8Zm18.9 2.1c-2 0-3.6-0.6-4.8-1.7c-1.2-1.1-1.7-2.6-1.7-4.6v-4.5c0-2 .5-3.5 1.7-4.6c1.2-1.1 2.8-1.7 4.8-1.7c1.9 0 3.5 .5 4.6 1.5c1.2 1 1.8 2.4 1.8 4.2h-2.7c0-1.1-0.4-1.9-1-2.4c-0.7-0.6-1.6-0.9-2.7-0.9c-1.2 0-2.1 .3-2.8 1c-0.7 .7-1.1 1.6-1.1 2.9l.1 4.5c-0.1 1.2 .3 2.2 1 2.9c.7 .7 1.6 1 2.8 1c1.1 0 2-0.3 2.7-0.9c.6-0.6 1-1.4 1-2.4h2.7c0 1.8-0.6 3.2-1.8 4.2c-1.1 1-2.7 1.5-4.6 1.5Zm17.9 0c-1.3 0-2.4-0.3-3.4-0.8c-1-0.5-1.7-1.2-2.3-2.2c-0.5-0.9-0.7-2-0.7-3.3v-4.5c0-1.3 .2-2.4 .7-3.3c.6-1 1.3-1.7 2.3-2.2c1-0.5 2.1-0.8 3.4-0.8c1.3 0 2.4 .3 3.4 .8c1 .5 1.7 1.2 2.3 2.2c.5 .9 .8 2 .8 3.3v2.9h-10.3v1.6c0 1.3 .3 2.3 1 3c.6 .7 1.6 1 2.8 1c1 0 1.9-0.2 2.5-0.5c.7-0.4 1.1-1 1.2-1.7h2.7c-0.2 1.4-0.9 2.5-2 3.3c-1.2 .8-2.7 1.2-4.4 1.2Zm-3.8-9.9h7.6v-0.9c0-1.3-0.3-2.3-1-3c-0.6-0.7-1.6-1-2.8-1c-1.2 0-2.2 .3-2.8 1c-0.7 .7-1 1.7-1 3Zm21.3 9.8c-1.7 0-3.1-0.3-4.2-1.1c-1.1-0.8-1.6-1.9-1.6-3.2h2.8c0 .6 .3 1.1 .8 1.4c.6 .4 1.4 .5 2.3 .5h1.2c1.1 0 1.9-0.2 2.4-0.6c.5-0.4 .8-1 .8-1.7c0-0.7-0.3-1.3-0.8-1.7c-0.5-0.4-1.2-0.7-2.1-0.8l-2.2-0.4c-1.6-0.3-2.9-0.8-3.6-1.5c-0.8-0.8-1.2-1.9-1.2-3.2c0-1.5 .5-2.6 1.4-3.4c1-0.8 2.4-1.2 4.2-1.2h1.2c1.7 0 3 .3 4 1.1c1 .7 1.5 1.7 1.5 3h-2.7c0-0.6-0.3-1-0.8-1.3c-0.5-0.3-1.2-0.4-2.1-0.4h-1.1c-1 0-1.7 .2-2.2 .5c-0.5 .4-0.8 1-0.8 1.7c0 1.2 .9 1.9 2.6 2.2l2.2 .4c1.8 .3 3.1 .8 3.9 1.5c.8 .8 1.2 1.9 1.2 3.3c0 1.6-0.5 2.8-1.5 3.6c-0.9 .9-2.4 1.3-4.3 1.3Zm38.3-0.2c-1.4 0-2.4-0.4-3.3-1.2c-0.8-0.8-1.2-1.8-1.2-3.2v-9.6h-4.6v-2.5h4.6v-4.7h2.7v4.7h6.6v2.5h-6.6v9.6c0 .6 .2 1.1 .5 1.4c.3 .4 .8 .5 1.3 .5h4.5v2.5Zm10 0v-21.9h2.7v8.6c.1-1.2 .6-2 1.3-2.6c.8-0.6 1.8-0.9 3.1-0.9c1.6 0 3 .5 3.9 1.5c1 1.1 1.5 2.4 1.5 4.2v11.1h-2.7v-10.8c0-1.2-0.3-2.1-0.9-2.7c-0.7-0.7-1.5-1-2.6-1c-1.1 0-2 .3-2.7 1c-0.6 .7-0.9 1.7-0.9 3v10.5Zm22.8 .3c-1.8 0-3.1-0.4-4.1-1.3c-1-0.9-1.5-2.1-1.5-3.7c0-1.5 .5-2.7 1.5-3.6c1-0.9 2.3-1.3 4-1.3h5.1v-1.7c0-1-0.3-1.7-0.9-2.3c-0.6-0.6-1.5-0.8-2.5-0.8c-1 0-1.8 .2-2.5 .6c-0.6 .4-1 1-1.2 1.7h-2.7c.1-1 .4-1.8 1-2.5c.6-0.7 1.3-1.2 2.2-1.6c1-0.4 2-0.6 3.2-0.6c1.9 0 3.4 .5 4.5 1.5c1.1 1 1.6 2.3 1.6 4v11.3h-2.6v-3.2h-0.1c-0.1 1.1-0.6 1.9-1.5 2.6c-0.9 .6-2.1 .9-3.5 .9Zm.6-2.1c1.3 0 2.4-0.4 3.2-1c.8-0.6 1.2-1.5 1.2-2.5v-2.3h-5c-0.9 0-1.6 .3-2.2 .8c-0.5 .5-0.7 1.1-0.7 2c0 .9 .3 1.6 .9 2.2c.6 .5 1.5 .8 2.6 .8Zm20.6 1.8c-1.4 0-2.4-0.4-3.3-1.2c-0.8-0.8-1.2-1.8-1.2-3.2v-9.6h-4.6v-2.5h4.6v-4.7h2.7v4.7h6.6v2.5h-6.6v9.6c0 .6 .2 1.1 .5 1.4c.3 .4 .8 .5 1.3 .5h4.5v2.5Zm28.5 0v-16.5h2.7v3.1c.1-1.1 .6-1.9 1.4-2.5c.7-0.6 1.7-0.9 3-0.9c1.7 0 3.1 .5 4.1 1.6c1 1.1 1.5 2.6 1.5 4.5v1.1h-2.7v-0.9c0-1.3-0.3-2.2-0.9-2.9c-0.7-0.7-1.6-1.1-2.8-1.1c-2.4 0-3.6 1.4-3.6 4v10.5Zm23.7 .3c-1.9 0-3.4-0.6-4.6-1.7c-1.1-1.1-1.7-2.7-1.7-4.6v-10.5h2.7v10.5c0 1.2 .3 2.2 1 2.9c.6 .7 1.5 1 2.6 1c1.1 0 2-0.3 2.6-1c.7-0.7 1-1.7 1-2.9v-10.5h2.7v10.5c0 1.9-0.6 3.5-1.7 4.6c-1.2 1.1-2.7 1.7-4.6 1.7Zm11.8-0.3v-16.5h2.7v3.1c.1-1.1 .6-1.9 1.3-2.5c.8-0.6 1.8-0.9 3.1-0.9c1.6 0 2.9 .5 3.9 1.5c1 1 1.5 2.4 1.5 4.2v11.1h-2.7v-10.8c0-1.2-0.3-2.1-0.9-2.7c-0.7-0.6-1.5-1-2.6-1c-1.1 0-2 .4-2.7 1.1c-0.6 .6-0.9 1.6-0.9 2.9v10.5Zm40.8 .3c-1.8 0-3.2-0.4-4.2-1.3c-1-0.9-1.5-2.1-1.5-3.7c0-1.5 .5-2.7 1.5-3.6c1-0.9 2.4-1.3 4.1-1.3h5.1v-1.7c0-1-0.3-1.7-0.9-2.3c-0.6-0.6-1.5-0.8-2.6-0.8c-0.9 0-1.7 .2-2.4 .6c-0.6 .4-1.1 1-1.2 1.6h-2.7c.1-0.9 .4-1.7 1-2.4c.6-0.7 1.3-1.2 2.2-1.6c1-0.4 2-0.6 3.1-0.6c1.9 0 3.4 .5 4.5 1.5c1.1 .9 1.7 2.3 1.7 4v11.3h-2.6v-3.2h-0.1c-0.1 1.1-0.6 1.9-1.5 2.6c-0.9 .6-2.1 .9-3.5 .9Zm.6-2.1c1.3 0 2.3-0.4 3.2-1c.8-0.6 1.2-1.5 1.2-2.5v-2.3h-5c-0.9 0-1.7 .3-2.2 .8c-0.5 .5-0.8 1.1-0.8 2c0 .9 .4 1.6 1 2.2c.6 .5 1.5 .8 2.6 .8Zm12.6 1.8v-16.5h2.7v3.1c.1-1.1 .6-1.9 1.3-2.5c.8-0.6 1.8-0.9 3.1-0.9c1.6 0 2.9 .5 3.9 1.5c1 1 1.5 2.4 1.5 4.2v11.1h-2.7v-10.8c0-1.2-0.3-2.1-0.9-2.7c-0.7-0.6-1.5-1-2.6-1c-1.1 0-2 .4-2.7 1.1c-0.6 .6-0.9 1.6-0.9 2.9v10.5Zm20.6 5.4l2.4-6.4l-6.3-15.5h2.9l4.1 10.2c.1 .3 .2 .7 .4 1.2c.1 .5 .2 .9 .3 1.2c.1-0.3 .2-0.7 .3-1.2c.1-0.5 .3-0.9 .4-1.2l3.8-10.2h2.8l-8.3 21.9Zm16.3-5.4l-2.7-16.5h2.4l1.6 11.7c.1 .4 .2 .9 .2 1.4c.1 .5 .1 1 .1 1.3h.2c0-0.3 0-0.8 .1-1.3c.1-0.5 .1-1 .2-1.4l1.8-11.7h2.7l1.9 11.7c.1 .4 .1 .9 .2 1.4c.1 .5 .1 1 .1 1.3h.2c0-0.3 .1-0.8 .1-1.3c.1-0.5 .2-1 .2-1.4l1.7-11.7h2.2l-2.7 16.5h-3l-1.8-11.4c0-0.6-0.1-1.1-0.2-1.7c-0.1-0.5-0.1-1-0.2-1.3h-0.1c0 .3-0.1 .8-0.1 1.3c-0.1 .6-0.2 1.1-0.3 1.7l-1.8 11.4Zm17.1 0v-21.9h2.7v8.5c.1-1.1 .6-1.9 1.3-2.5c.8-0.6 1.8-0.9 3.1-0.9c1.6 0 2.9 .5 3.9 1.5c1 1 1.5 2.4 1.5 4.2v11.1h-2.7v-10.8c0-1.2-0.3-2.1-0.9-2.7c-0.7-0.7-1.5-1-2.6-1c-1.1 0-2 .3-2.7 1c-0.6 .7-0.9 1.7-0.9 3v10.5Zm24.2 .3c-1.3 0-2.4-0.3-3.4-0.8c-1-0.5-1.7-1.2-2.3-2.2c-0.5-0.9-0.8-2-0.8-3.3v-4.5c0-1.3 .3-2.4 .8-3.3c.6-1 1.3-1.7 2.3-2.2c1-0.5 2.1-0.8 3.4-0.8c1.3 0 2.4 .3 3.4 .8c1 .5 1.7 1.2 2.3 2.2c.5 .9 .7 2 .7 3.3v2.9h-10.2v1.6c0 1.3 .3 2.3 1 3c.6 .7 1.6 1 2.8 1c1 0 1.9-0.2 2.5-0.5c.7-0.4 1-1 1.2-1.7h2.7c-0.2 1.4-0.9 2.5-2 3.3c-1.2 .8-2.7 1.2-4.4 1.2Zm-3.8-9.9h7.6v-0.9c0-1.3-0.3-2.3-1-3c-0.6-0.7-1.6-1.1-2.8-1.1c-1.2 0-2.2 .4-2.8 1.1c-0.7 .7-1 1.7-1 3Zm16.1 9.6v-16.5h2.7v3.1c.1-1.1 .6-1.9 1.3-2.5c.8-0.6 1.8-0.9 3.1-0.9c1.7 0 3.1 .5 4.1 1.6c1 1.1 1.5 2.6 1.5 4.5v1.1h-2.7v-0.9c0-1.3-0.3-2.2-0.9-2.9c-0.7-0.7-1.6-1.1-2.8-1.1c-2.4 0-3.6 1.4-3.6 4v10.5Zm23.7 .3c-1.3 0-2.4-0.3-3.4-0.8c-1-0.5-1.7-1.2-2.3-2.2c-0.5-0.9-0.8-2-0.8-3.3v-4.5c0-1.3 .3-2.4 .8-3.3c.6-1 1.3-1.7 2.3-2.2c1-0.5 2.1-0.8 3.4-0.8c1.3 0 2.4 .3 3.4 .8c1 .5 1.7 1.2 2.3 2.2c.5 .9 .7 2 .7 3.3v2.9h-10.2v1.6c0 1.3 .3 2.3 1 3c.6 .7 1.6 1 2.8 1c1 0 1.9-0.2 2.5-0.5c.7-0.4 1-1 1.2-1.7h2.7c-0.2 1.4-0.9 2.5-2 3.3c-1.2 .8-2.7 1.2-4.4 1.2Zm-3.8-9.9h7.6v-0.9c0-1.3-0.3-2.3-1-3c-0.6-0.7-1.6-1.1-2.8-1.1c-1.2 0-2.2 .4-2.8 1.1c-0.7 .7-1 1.7-1 3Z"/><g transform="translate(764,-14.5) scale(.431111,.440249) translate(-13.9,56.6)"><path fill="#e0e0e0" stroke="#e0e0e0" stroke-linejoin="round" d="M-50.7 4h278.2c10 0 9 0 9 0l41 40c0 0 1 0-9 0h-358c-9.9 0-9 0-9 0l38.8-40c0 0-0.9 0 9 0Z" stroke-width="10" transform="translate(225,339.9) scale(0,1) translate(-89.5,-24)" style="animation: 10s linear infinite both a0_t;"/><rect width="83" height="22" stroke="#fb422d" fill="#e0e0e0" rx="2" stroke-width="0" transform="translate(225,348.9) scale(0,1) translate(-41.5,-11)" style="animation: 10s linear infinite both a1_t;"/><g style="animation: 10s linear infinite both a3_t;"><rect width="400" height="334" stroke="#e0e0e0" fill="none" stroke-width="30" stroke-miterlimit="1" rx="10" opacity="0" transform="translate(225,198.9) rotate(-90) translate(-200,-167)" style="animation: 10s linear infinite both a2_t, 10s linear infinite both a2_o, 10s linear infinite both a2_sw, 10s linear infinite both a2_w, 10s linear infinite both a2_h;"/></g><use width="168.5" height="302" xlink:href="#Symbol-2" opacity="0" transform="translate(227.8,199.9) translate(-84.2,-151)" style="animation: 10s linear infinite both a4_t, 10s linear infinite both a4_w, 10s linear infinite both a4_h;"/><rect width="16" height="8" stroke="#fb422d" fill="#e0e0e0" stroke-width="0" rx="2" opacity="0" transform="translate(225,328.9) translate(-8,-4)" style="animation: 10s linear infinite both a5_t, 10s linear infinite both a5_o, 10s linear infinite both a5_w;"/><rect width="16" height="8" stroke="#fb422d" fill="#e0e0e0" stroke-width="0" rx="2" opacity="0" transform="translate(225,328.9) translate(-8,-4)" style="animation: 10s linear infinite both a6_t, 10s linear infinite both a6_o, 10s linear infinite both a6_w;"/><g transform="translate(225,198.9) scale(1.5,1.5) translate(-42.3,-93)" style="animation: 10s linear infinite both a7_t;"><g transform="translate(42.3,93) scale(5.352,6.9) translate(-16,60)"><g transform="translate(-34.9,-125.8)"><g transform="translate(24,5)"><path d="M33 46.5c0 3.1-0.8 5.5-2.2 7.4c-1.4 1.9-3.3 3.6-5.4 5.4c-2.2 1.7-4.5 3.6-6.3 6.2c-1.8 2.5-3.1 5.5-3.1 9.5h4.7c0-3.1 .9-5.4 2.2-7.3c1.4-1.9 3.3-3.5 5.5-5.3c2.1-1.8 4.4-3.7 6.2-6.2c1.8-2.6 3.1-5.6 3.1-9.7Z" fill="#e96020"/><path d="M20.4 70.7c-0.6 0-1 .5-1 1.1c0 .7 .4 1.2 1 1.2h12.9c.7 0 1.2-0.5 1.2-1.2c0-0.6-0.5-1.1-1.2-1.1Z" fill="#2d323b"/><path d="M21.8 66.6c-0.7 0-1 .6-1 1.2c0 .6 .3 1 .9 1h10.3c.6 0 1.1-0.5 1.1-1.1c0-0.6-0.5-1.1-1.1-1.1Z" fill="#2d323b"/><path d="M21.8 53c-0.7 0-1.2 .5-1.1 1.1c0 .6 .4 1.1 1.1 1.1h10.2c.6 0 .9-0.6 .9-1.2c0-0.6-0.3-1-0.9-1Z" fill="#2d323b"/><path d="M20.4 48.8c-0.6 0-1.2 .5-1.2 1.1c0 .7 .6 1.2 1.2 1.2h12.9c.7 0 1-0.5 1-1.1c0-0.7-0.3-1.2-1-1.2Z" fill="#2d323b"/><path d="M16 46.5c0 4.1 1.3 7.1 3.1 9.7c1.8 2.5 4.1 4.4 6.3 6.2c2.1 1.8 4 3.6 5.4 5.5c1.4 1.9 2.2 4 2.2 7.1h4.7c0-4-1.3-7.1-3.1-9.6c-1.8-2.5-4.1-4.4-6.2-6.2c-2.2-1.8-4.1-3.4-5.5-5.3c-1.3-1.9-2.2-4.3-2.2-7.4Z" fill="#00a8d6"/></g></g></g></g></g></svg>
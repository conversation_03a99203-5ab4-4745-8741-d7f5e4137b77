// Service worker for background processing, notifications, and offline support

const CACHE_NAME = 'dog-in-fit-v1';
const ASSETS_TO_CACHE = [
  '/',
  '/index.html',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  '/icons/badge-96x96.png',
  '/icons/walk-96x96.png',
  '/icons/apple-touch-icon.png',
  // Add CSS and JS files that will be auto-injected by the build process
];

let walkInterval = null;
let walkData = null;

// Install event - cache assets
self.addEventListener('install', (event) => {
  self.skipWaiting();
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      return cache.addAll(ASSETS_TO_CACHE);
    })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if(cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  return self.clients.claim();
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', (event) => {
  // // Skip cross-origin requests
  // if(!event.request.url.startsWith(self.location.origin)) {
  //   return;
  // }

  // // Skip Supabase API requests
  // if(event.request.url.includes('supabase.co')) {
  //   return;
  // }

  event.respondWith(
    caches.match(event.request).then((cachedResponse) => {
      // Return cached response if available
      if(cachedResponse) {
        return cachedResponse;
      }

      // Otherwise try to fetch from network
      return fetch(event.request)
        .then((response) => {
          // Don't cache if not a valid response
          if(!response || response.status !== 200 || response.type !== 'basic') {
            return response;
          }

          // Clone the response as it can only be consumed once
          const responseToCache = response.clone();

          // Add the new response to cache
          caches.open(CACHE_NAME)
            .then((cache) => {
              cache.put(event.request, responseToCache);
            });

          return response;
        })
        .catch(() => {
          // If both cache and network fail, return a fallback for HTML requests
          if(event.request.headers.get('accept').includes('text/html')) {
            return caches.match('/index.html');
          }

          // Return a simple offline response for other assets
          return new Response('Offline content not available', {
            status: 503,
            statusText: 'Service Unavailable',
            headers: new Headers({
              'Content-Type': 'text/plain'
            })
          });
        });
    })
  );
});

// Message event - handle messages from the main thread
self.addEventListener('message', async (event) => {
  self.registration.showNotification('Notifications Enabled', {
    body: 'You will now receive updates about your dog walks on iOS.',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-96x96.png',
    // iOS requires these options for interactive notifications
    actions: [
      {action: 'test-action', title: 'Test Controls'}
    ],
    // Make it silent for the test
    silent: true
  });

  if(event.data && event.data.type === 'START_WALK') {
    // Start tracking walk in the background
    walkData = {
      startTime: event.data.startTime || Date.now(),
      duration: event.data.duration || 0,
      isWalking: true,
      statusChangeTime: event.data.statusChangeTime || Date.now(), // Use provided time or current time
      platform: event.data.platform || 'other' // Store platform information
    };

    // Create a persistent notification with platform-specific options
    showWalkNotification(walkData);

    // Start interval to update the notification
    if(!walkInterval) {
      walkInterval = setInterval(() => {
        if(walkData && walkData.isWalking) {
          // Calculate elapsed time since status change
          const now = Date.now();
          const elapsedSeconds = Math.floor((now - walkData.statusChangeTime) / 1000);

          // Add elapsed time to the duration that was saved at status change
          const currentDuration = walkData.duration + elapsedSeconds;

          showWalkNotification({...walkData, currentDuration});

          // Send updated data back to all clients
          self.clients.matchAll().then(clients => {
            clients.forEach(client => {
              client.postMessage({
                type: 'WALK_UPDATE',
                duration: walkData.duration,
                statusChangeTime: walkData.statusChangeTime,
                isWalking: walkData.isWalking
              });
            });
          });
        }
      }, 1000);
    }
  }
  else if(event.data && event.data.type === 'PAUSE_WALK') {
    if(walkData) {
      // Calculate elapsed time since last status change
      const now = Date.now();
      const elapsedSeconds = Math.floor((now - walkData.statusChangeTime) / 1000);

      // Update the base duration by adding elapsed time
      walkData.duration += elapsedSeconds;
      walkData.isWalking = false;
      walkData.statusChangeTime = now; // Track when we paused

      showWalkNotification(walkData, 'paused');

      // Send updated data back to all clients
      self.clients.matchAll().then(clients => {
        clients.forEach(client => {
          client.postMessage({
            type: 'WALK_PAUSED',
            duration: walkData.duration,
            statusChangeTime: walkData.statusChangeTime
          });
        });
      });
    }
  }
  else if(event.data && event.data.type === 'CONTINUE_WALK') {
    if(walkData) {
      walkData.isWalking = true;
      walkData.statusChangeTime = now = Date.now(); // Track when we continued

      showWalkNotification(walkData);

      // Send updated data back to all clients
      self.clients.matchAll().then(clients => {
        clients.forEach(client => {
          client.postMessage({
            type: 'WALK_CONTINUED',
            duration: walkData.duration,
            statusChangeTime: walkData.statusChangeTime
          });
        });
      });
    }
  }
  else if(event.data && event.data.type === 'END_WALK') {
    // Stop tracking and clear notification
    if(walkInterval) {
      clearInterval(walkInterval);
      walkInterval = null;
    }

    if(walkData) {
      // Calculate final duration
      const now = Date.now();
      if(walkData.isWalking) {
        const elapsedSeconds = Math.floor((now - walkData.statusChangeTime) / 1000);
        walkData.duration += elapsedSeconds;
      }

      // Send final data to client
      self.clients.matchAll().then(clients => {
        clients.forEach(client => {
          client.postMessage({
            type: 'WALK_COMPLETED',
            duration: walkData.duration
          });
        });
      });

      walkData = null;
      self.registration.getNotifications().then(notifications => {
        notifications.forEach(notification => notification.close());
      });
    }
  }
  else if(event.data && event.data.type === 'GET_WALK_STATE') {
    // If we have walk data, calculate current duration
    if(walkData) {
      let currentDuration = walkData.duration;

      // Only add elapsed time if currently walking
      if(walkData.isWalking) {
        const now = Date.now();
        const elapsedSeconds = Math.floor((now - walkData.statusChangeTime) / 1000);
        currentDuration += elapsedSeconds;
      }

      // Send current walk state to the client that just opened the app
      if(event.source) {
        event.source.postMessage({
          type: 'WALK_STATE',
          walkData: {
            ...walkData,
            currentDuration
          }
        });
      }
    } else {
      // No active walk
      if(event.source) {
        event.source.postMessage({
          type: 'WALK_STATE',
          walkData: null
        });
      }
    }
  }
  else if(event.data && event.data.type === 'SYNC_WALK_STATE') {
    // Update walkData with data from client (used after page reload)
    if(event.data.walkData) {
      walkData = event.data.walkData;

      // If walking, update the notification
      if(walkData.isWalking) {
        showWalkNotification(walkData);

        // Ensure interval is running
        if(!walkInterval) {
          walkInterval = setInterval(() => {
            if(walkData && walkData.isWalking) {
              // Calculate elapsed time since status change
              const now = Date.now();
              const elapsedSeconds = Math.floor((now - walkData.statusChangeTime) / 1000);
              const currentDuration = walkData.duration + elapsedSeconds;

              showWalkNotification({...walkData, currentDuration});
            }
          }, 1000);
        }
      } else {
        showWalkNotification(walkData, 'paused');
      }
    }
  }
  // Add handling for test notifications
  else if(event.data && event.data.type === 'TEST_NOTIFICATION') {
    // Show a test notification
    self.registration.showNotification('Notifications Enabled', {
      body: 'You will now receive updates about your dog walks.',
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-96x96.png'
    });
  }
  // Add handling for Android background permission check
  else if(event.data && event.data.type === 'CHECK_BACKGROUND_PERMISSION') {
    // On Android, we can check if we can use background sync
    if('sync' in self.registration) {
      // Try to register a background sync
      self.registration.sync.register('walk-background-sync').then(() => {
        console.log('Background sync registered successfully');

        // Notify the client that background sync is available
        event.source.postMessage({
          type: 'BACKGROUND_PERMISSION_STATUS',
          status: 'granted'
        });
      }).catch(error => {
        console.error('Background sync registration failed:', error);

        // Notify the client that background sync failed
        event.source.postMessage({
          type: 'BACKGROUND_PERMISSION_STATUS',
          status: 'denied',
          error: error.message
        });
      });
    } else {
      console.log('Background sync not supported');

      // Notify the client that background sync is not supported
      event.source.postMessage({
        type: 'BACKGROUND_PERMISSION_STATUS',
        status: 'not_supported'
      });
    }
  }
  // Add handling for iOS push registration with better logging
  else if(event.data && event.data.type === 'REGISTER_IOS_PUSH') {
    console.log('Received REGISTER_IOS_PUSH message in service worker');

    // For iOS, we need to show a test notification to ensure permissions are working
    self.registration.showNotification('Notifications Enabled', {
      body: 'You will now receive updates about your dog walks on iOS.',
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-96x96.png',
      // iOS requires these options for interactive notifications
      actions: [
        {action: 'test-action', title: 'Test Controls'}
      ],
      // Make it silent for the test
      silent: true
    }).then(() => {
      console.log('iOS test notification shown successfully');

      // Notify the client that iOS push is registered
      if(event.source) {
        event.source.postMessage({
          type: 'IOS_PUSH_STATUS',
          status: 'registered'
        });
      }
    }).catch(error => {
      console.error('Error showing iOS test notification:', error);

      // Notify the client that iOS push failed
      if(event.source) {
        event.source.postMessage({
          type: 'IOS_PUSH_STATUS',
          status: 'failed',
          error: error.message
        });
      }
    });
  }
  // Add handling for debug info requests
  else if(event.data && event.data.type === 'DEBUG_INFO_REQUEST') {
    console.log('=== SERVICE WORKER DEBUG INFO ===');
    console.log('Walk Data:', walkData);
    console.log('Walk Interval Active:', !!walkInterval);
    console.log('Clients:', await self.clients.matchAll());
    console.log('Registration:', self.registration);

    // Send debug info back to the client
    if(event.source) {
      event.source.postMessage({
        type: 'DEBUG_INFO_RESPONSE',
        walkData: walkData,
        hasInterval: !!walkInterval,
        scope: self.registration.scope,
        timestamp: Date.now()
      });
    }
  }
});

// Function to show walk notification
function showWalkNotification(data, status = 'active') {
  // Check if we can show notifications
  if(!self.registration || !('showNotification' in self.registration)) {
    console.log('Notification API not available, skipping notification');
    return;
  }

  if(self.Notification.permission !== 'granted') {
    console.log('Notification permission not granted, skipping notification');
    return;
  }

  // Calculate current duration for display
  let displayDuration = data.duration;

  // If walking, add elapsed time since status change
  if(status === 'active' && data.isWalking) {
    const now = Date.now();
    const elapsedSeconds = Math.floor((now - data.statusChangeTime) / 1000);
    displayDuration += elapsedSeconds;
  }

  // If we received pre-calculated current duration, use that instead
  if(data.currentDuration !== undefined) {
    displayDuration = data.currentDuration;
  }

  const minutes = Math.floor(displayDuration / 60);
  const seconds = displayDuration % 60;
  const timeString = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

  const title = status === 'paused'
    ? 'Walk Paused'
    : 'Walk in Progress';

  // Base notification options
  const options = {
    body: `Duration: ${timeString}`,
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-96x96.png',
    tag: 'walk-tracker',
    renotify: true,
    actions: [
      status === 'paused'
        ? {action: 'continue', title: 'Continue'}
        : {action: 'pause', title: 'Pause'},
      {action: 'end', title: 'End Walk'}
    ],
    // This makes the notification persistent
    requireInteraction: true,
    // Add a timestamp to show when the notification was last updated
    timestamp: Date.now()
  };

  // Platform-specific adjustments
  if(data.platform === 'ios') {
    console.log('Showing iOS-specific notification');
    // iOS has specific requirements for notifications
    options.vibrate = []; // iOS ignores this but we set it empty to be safe
    options.silent = false; // Ensure sound plays on iOS
    // iOS requires simpler actions
    options.actions = [
      {
        action: status === 'paused' ? 'continue' : 'pause',
        title: status === 'paused' ? 'Continue' : 'Pause'
      },
      {action: 'end', title: 'End'}
    ];
  } else {
    // Android and other platforms
    options.vibrate = status === 'paused' ? [0] : [10, 10];
    options.silent = false;
    options.priority = 'high';
  }

  // Try to show notification, but catch any errors
  try {
    console.log('Showing notification with options:', {title, ...options});
    self.registration.showNotification(title, options)
      .then(() => console.log('Notification shown successfully'))
      .catch(error => {
        console.error('Error showing notification:', error);
      });
  } catch(error) {
    console.error('Error showing notification:', error);
  }
}

// Handle notification actions
self.addEventListener('notificationclick', (event) => {
  const notificationAction = event.action;

  // If no specific action was clicked (i.e., the notification body was clicked)
  if(!notificationAction) {
    // Focus on the app window or open a new one
    event.waitUntil(
      self.clients.matchAll({type: 'window'}).then(clientList => {
        if(clientList.length > 0) {
          return clientList[0].focus();
        }
        return self.clients.openWindow('/');
      })
    );
    return;
  }

  // Handle specific notification actions
  if(notificationAction === 'pause') {
    if(walkData) {
      // Calculate elapsed time since last status change
      const now = Date.now();
      const elapsedSeconds = Math.floor((now - walkData.statusChangeTime) / 1000);

      // Update the base duration by adding elapsed time
      walkData.duration += elapsedSeconds;
      walkData.isWalking = false;
      walkData.statusChangeTime = now; // Track when we paused

      showWalkNotification(walkData, 'paused');

      // Notify all clients
      self.clients.matchAll().then(clients => {
        clients.forEach(client => {
          client.postMessage({
            type: 'WALK_PAUSED',
            duration: walkData.duration,
            statusChangeTime: walkData.statusChangeTime
          });
        });
      });
    }
  }
  else if(notificationAction === 'continue') {
    if(walkData) {
      walkData.isWalking = true;
      walkData.statusChangeTime = Date.now(); // Track when we continued

      showWalkNotification(walkData);

      // Notify all clients
      self.clients.matchAll().then(clients => {
        clients.forEach(client => {
          client.postMessage({
            type: 'WALK_CONTINUED',
            duration: walkData.duration,
            statusChangeTime: walkData.statusChangeTime
          });
        });
      });
    }
  }
  else if(notificationAction === 'end') {
    // End the walk
    if(walkInterval) {
      clearInterval(walkInterval);
      walkInterval = null;
    }

    // Calculate final duration
    if(walkData) {
      const now = Date.now();
      if(walkData.isWalking) {
        const elapsedSeconds = Math.floor((now - walkData.statusChangeTime) / 1000);
        walkData.duration += elapsedSeconds;
      }

      // Notify all clients
      self.clients.matchAll().then(clients => {
        clients.forEach(client => {
          client.postMessage({
            type: 'WALK_ENDED',
            duration: walkData.duration
          });
        });
      });

      walkData = null;
    }

    event.notification.close();
  }
});


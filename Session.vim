let SessionLoad = 1
let s:so_save = &g:so | let s:siso_save = &g:siso | setg so=0 siso=0 | setl so=-1 siso=-1
let v:this_session=expand("<sfile>:p")
let Db_ui_buffer_name_generator =  0 
let Db_ui_table_name_sorter =  0 
silent only
silent tabonly
cd ~
if expand('%') == '' && !&modified && line('$') <= 1 && getline(1) == ''
  let s:wipebuf = bufnr('%')
endif
let s:shortmess_save = &shortmess
if &shortmess =~ 'A'
  set shortmess=aoOA
else
  set shortmess=aoO
endif
badd +61 Development/Projects/MyDogInFit/app/src/utils/storage/local_storage.rs
badd +44 ~/Development/Projects/MyDogInFit/app/src/ui/components/page_header.rs
badd +66 ~/Development/Projects/MyDogInFit/app/src/ui/pages/home_page.rs
badd +30 ~/Development/Projects/MyDogInFit/app/src/ui/components/treat_tracker.rs
argglobal
%argdel
$argadd Development/Projects/MyDogInFit/app/src/utils/storage/local_storage.rs
edit ~/Development/Projects/MyDogInFit/app/src/ui/pages/home_page.rs
let s:save_splitbelow = &splitbelow
let s:save_splitright = &splitright
set splitbelow splitright
wincmd _ | wincmd |
vsplit
1wincmd h
wincmd w
let &splitbelow = s:save_splitbelow
let &splitright = s:save_splitright
wincmd t
let s:save_winminheight = &winminheight
let s:save_winminwidth = &winminwidth
set winminheight=0
set winheight=1
set winminwidth=0
set winwidth=1
wincmd =
argglobal
setlocal foldmethod=expr
setlocal foldexpr=v:lua.require'lazyvim.util'.ui.foldexpr()
setlocal foldmarker={{{,}}}
setlocal foldignore=#
setlocal foldlevel=99
setlocal foldminlines=1
setlocal foldnestmax=20
setlocal foldenable
let s:l = 66 - ((59 * winheight(0) + 40) / 81)
if s:l < 1 | let s:l = 1 | endif
keepjumps exe s:l
normal! zt
keepjumps 66
normal! 028|
wincmd w
argglobal
if bufexists(fnamemodify("~/Development/Projects/MyDogInFit/app/src/ui/pages/home_page.rs", ":p")) | buffer ~/Development/Projects/MyDogInFit/app/src/ui/pages/home_page.rs | else | edit ~/Development/Projects/MyDogInFit/app/src/ui/pages/home_page.rs | endif
if &buftype ==# 'terminal'
  silent file ~/Development/Projects/MyDogInFit/app/src/ui/pages/home_page.rs
endif
balt ~/Development/Projects/MyDogInFit/app/src/ui/components/treat_tracker.rs
setlocal foldmethod=expr
setlocal foldexpr=v:lua.require'lazyvim.util'.ui.foldexpr()
setlocal foldmarker={{{,}}}
setlocal foldignore=#
setlocal foldlevel=99
setlocal foldminlines=1
setlocal foldnestmax=20
setlocal foldenable
let s:l = 64 - ((56 * winheight(0) + 40) / 81)
if s:l < 1 | let s:l = 1 | endif
keepjumps exe s:l
normal! zt
keepjumps 64
normal! 012|
wincmd w
2wincmd w
wincmd =
tabnext 1
if exists('s:wipebuf') && len(win_findbuf(s:wipebuf)) == 0 && getbufvar(s:wipebuf, '&buftype') isnot# 'terminal'
  silent exe 'bwipe ' . s:wipebuf
endif
unlet! s:wipebuf
set winheight=1 winwidth=20
let &shortmess = s:shortmess_save
let &winminheight = s:save_winminheight
let &winminwidth = s:save_winminwidth
let s:sx = expand("<sfile>:p:r")."x.vim"
if filereadable(s:sx)
  exe "source " . fnameescape(s:sx)
endif
let &g:so = s:so_save | let &g:siso = s:siso_save
set hlsearch
nohlsearch
doautoall SessionLoadPost
unlet SessionLoad
" vim: set ft=vim :
